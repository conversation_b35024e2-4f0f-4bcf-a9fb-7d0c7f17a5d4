import os
import json
from flask import Flask, render_template, jsonify, request, send_from_directory, send_file, redirect, url_for
from datetime import datetime
from io import BytesIO
import xlsxwriter

app = Flask(__name__, static_folder='static')

# 数据文件路径配置
DATA_DIR = 'data'
PROJECT_DATA_FILE = os.path.join(DATA_DIR, 'project_data.json')
BENTI_DATA_FILE = os.path.join(DATA_DIR, 'Ind_benti.json')
QITA_DATA_FILE = os.path.join(DATA_DIR, 'Ind_qita.json')
PRICING_PROJECTS_FILE = os.path.join(DATA_DIR, 'pricing_projects.json')
FEATURE_SECTIONS_FILE = os.path.join(DATA_DIR, 'feature_sections.json')
PRICING_RESULTS_FILE = os.path.join(DATA_DIR, 'pricing_results.json')

# 确保data目录存在
os.makedirs(DATA_DIR, exist_ok=True)

def load_json_data(file_path):
    """通用JSON数据加载函数"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        if file_path == PRICING_PROJECTS_FILE:
            return {"projects": []}
        return []
    except Exception as e:
        print(f"加载数据失败 {file_path}: {e}")
        return []

def save_json_data(file_path, data):
    """通用JSON数据保存函数"""
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, ensure_ascii=False, indent=4, fp=f)
        return True
    except Exception as e:
        print(f"保存数据失败 {file_path}: {e}")
        return False

# 初始化数据文件
if not os.path.exists(PROJECT_DATA_FILE):
    save_json_data(PROJECT_DATA_FILE, [])

if not os.path.exists(PRICING_PROJECTS_FILE):
    save_json_data(PRICING_PROJECTS_FILE, {"projects": []})

if not os.path.exists(FEATURE_SECTIONS_FILE):
    save_json_data(FEATURE_SECTIONS_FILE, {"feature_sections": []})

def load_project_data():
    """加载项目数据"""
    return load_json_data(PROJECT_DATA_FILE)

def save_project_data(data):
    """保存项目数据"""
    try:
        with open(PROJECT_DATA_FILE, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=4)
        return True
    except Exception as e:
        print(f"保存数据失败 {PROJECT_DATA_FILE}: {e}")
        return False

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/his_ind/query')
def his_ind_query():
    # 加载本体费用和其他费用的JSON数据
    benti_data = load_json_data(BENTI_DATA_FILE)
    qita_data = load_json_data(QITA_DATA_FILE)
    
    return render_template('his_ind/hisIndQuery.html', 
                         benti_data=benti_data,
                         qita_data=qita_data)

# 保留旧的智能辅助校审页面，入口，暂时保留
# @app.route('/audit/project/workspace')
# def project_workspace():
#     return render_template('audit/project/projectWorkspace.html')

# 新入口
@app.route('/audit')
def audit_index():
    return render_template('audit/auditIndex.html')


@app.route('/audit/indicatorUpload.html')
def indicator_upload():
    return render_template('audit/indicatorUpload.html')

@app.route('/ruleAudit.html')
def rule_audit():
    return render_template('rule/ruleAudit.html')

@app.route('/settings.html')
def settings():
    return render_template('settings/settings.html')

# 获取本体费用指标-指标数据预览
@app.route('/api/indicators/benti')
def get_benti_indicators():
    try:
        # 从数据文件中读取本体费用指标
        benti_data = load_json_data(BENTI_DATA_FILE)
        if isinstance(benti_data, list) and len(benti_data) > 0:
            first_row = benti_data[0]
            if isinstance(first_row, dict):
                # 只返回第一行数据
                return jsonify([first_row])
        return jsonify([])
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# 获取其他费用指标-指标数据预览
@app.route('/api/indicators/qita')
def get_qita_indicators():
    try:
        # 从数据文件中读取其他费用指标
        qita_data = load_json_data(QITA_DATA_FILE)
        if isinstance(qita_data, list) and len(qita_data) > 0:
            first_row = qita_data[0]
            if isinstance(first_row, dict):
                # 只返回第一行数据
                return jsonify([first_row])
        return jsonify([])
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# 加载本体费用校审数据
@app.route('/api/review/benti/<int:project_id>')
def get_benti_review(project_id):
    # 这里暂时返回第一条数据作为示例
    # 实际项目中应该根据project_id查询对应的校审数据
    benti_data = load_json_data(BENTI_DATA_FILE)
    if benti_data:
        data = benti_data[0]
        # 模拟校审结果
        review_data = {}
        for key, value in data.items():
            if isinstance(value, (int, float)) and key not in ['序号']:
                is_abnormal = value > 80  # 示例：值大于80视为异常
                # 生成随机的历史指标值区间
                min_value = 25
                max_value = 45
                review_data[key] = {
                    'value': value,
                    'status': '异常' if is_abnormal else '正常',
                    'detail': f'指标值偏离历史均值较大：历史指标值区间为【{min_value}-{max_value}】，现指标值为{value}，异常。' if is_abnormal else f'指标值在合理范围内：历史指标值区间为【{min_value}-{max_value}】，现指标值为{value}，正常。'
                }
        return jsonify(review_data)
    return jsonify({})

# 加载其他费用校审数据
@app.route('/api/review/qita/<int:project_id>')
def get_qita_review(project_id):
    # 这里暂时返回第一条数据作为示例
    # 实际项目中应该根据project_id查询对应的校审数据
    qita_data = load_json_data(QITA_DATA_FILE)
    if qita_data:
        data = qita_data[0]
        # 模拟校审结果
        review_data = {}
        for key, value in data.items():
            if isinstance(value, (int, float)) and key not in ['序号']:
                is_abnormal = value > 80  # 示例：值大于80视为异常
                # 生成随机的历史指标值区间
                min_value = 25
                max_value = 45
                review_data[key] = {
                    'value': value,
                    'status': '异常' if is_abnormal else '正常',
                    'detail': f'指标值偏离历史均值较大：历史指标值区间为【{min_value}-{max_value}】，现指标值为{value}，异常。' if is_abnormal else f'指标值在合理范围内：历史指标值区间为【{min_value}-{max_value}】，现指标值为{value}，正常。'
                }
        return jsonify(review_data)
    return jsonify({})

# API接口：获取所有项目
@app.route('/api/projects', methods=['GET'])
def get_projects():
    projects = load_project_data()
    
    # 复制原始数据，避免修改原始数据
    sorted_projects = projects.copy()
    
    # 按创建时间倒序排序
    sorted_projects.sort(key=lambda x: x.get('创建时间', ''), reverse=True)
    
    return jsonify(sorted_projects)

# API接口：创建新项目
@app.route('/api/projects', methods=['POST'])
def create_project():
    try:
        projects = load_project_data()
        new_project = request.json
        
        # 如果新项目没有序号，则使用当前最大序号+1
        if '序号' not in new_project:
            max_seq = max((p.get('序号', 0) for p in projects), default=0)
            new_project['序号'] = max_seq + 1
        
        # 添加到项目列表
        projects.append(new_project)
        
        # 保存数据
        if save_project_data(projects):
            return jsonify({"message": "项目创建成功"}), 201
        else:
            return jsonify({"error": "保存数据失败"}), 500
            
    except Exception as e:
        return jsonify({"error": str(e)}), 500

# API接口：更新项目状态
@app.route('/api/projects/<int:project_id>', methods=['PATCH'])
def update_project(project_id):
    """
    更新项目状态
    注意：以下匹配逻辑仅用于演示！
    在实际项目中应该：
    1. 使用唯一标识符（如项目ID）来匹配项目
    2. 实现完整的文件导入和解析逻辑
    3. 根据实际导入的文件内容更新状态
    """
    try:
        projects = load_project_data()
        updates = request.json
        target_project = None
        
        # 查找并更新项目
        for project in projects:
            if project['序号'] == project_id:
                target_project = project
                # 如果是重新导入（通过检查导入状态判断）
                if updates.get('导入状态') == '已导入' and project.get('导入状态') == '已导入':
                    # 获取当前项目的创建时间
                    creation_time = project.get('创建时间', '')
                    
                    # 遍历所有项目，重置具有相同创建时间的项目状态
                    # 注意：这种匹配方式仅用于演示！实际项目中应使用更可靠的标识符
                    for p in projects:
                        if p.get('创建时间') == creation_time:
                            p['提取状态'] = '未提取'
                            p['校审状态'] = '未校审'
                            p['校审时间'] = ''
                
                # 更新当前项目的其他状态
                allowed_fields = ['导入状态', '提取状态', '校审状态', '校审时间']
                for field in allowed_fields:
                    if field in updates:
                        project[field] = updates[field]
                break
        
        if target_project is None:
            return jsonify({"error": "项目不存在"}), 404
        
        # 保存数据
        if save_project_data(projects):
            return jsonify({"message": "项目更新成功"})
        else:
            return jsonify({"error": "保存数据失败"}), 500
            
    except Exception as e:
        print(f"更新项目状态失败: {str(e)}")  # 添加错误日志
        return jsonify({"error": str(e)}), 500

# 新增：指标提取状态更新接口
@app.route('/api/projects/<int:project_id>/extract', methods=['POST'])
def extract_indicators(project_id):
    try:
        # 模拟指标提取过程
        # 在实际项目中，这里应该是真实的指标提取逻辑
        benti_data = load_json_data(BENTI_DATA_FILE)
        qita_data = load_json_data(QITA_DATA_FILE)
        
        if not benti_data or not qita_data:
            return jsonify({"error": "无法加载指标数据"}), 500
            
        # 更新项目状态
        projects = load_project_data()
        for project in projects:
            if project['序号'] == project_id:
                project['提取状态'] = '已提取'
                break
        
        # 保存更新后的项目数据
        if save_project_data(projects):
            return jsonify({
                "message": "指标提取成功",
                "status": "success"
            })
        else:
            return jsonify({"error": "保存数据失败"}), 500
            
    except Exception as e:
        return jsonify({"error": str(e)}), 500

# 新增：指标校审状态更新接口
@app.route('/api/projects/<int:project_id>/review', methods=['POST'])
def review_indicators(project_id):
    try:
        # 模拟校审过程
        # 在实际项目中，这里应该是真实的校审逻辑
        benti_data = load_json_data(BENTI_DATA_FILE)
        qita_data = load_json_data(QITA_DATA_FILE)
        
        if not benti_data or not qita_data:
            return jsonify({"error": "无法加载指标数据"}), 500
            
        # 更新项目状态
        projects = load_project_data()
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        for project in projects:
            if project['序号'] == project_id:
                project['校审状态'] = '已校审'
                project['校审时间'] = current_time
                break
        
        # 保存更新后的项目数据
        if save_project_data(projects):
            # 生成校审结果数据
            review_data = {
                'benti': {},
                'qita': {}
            }
            
            # 处理本体费用指标校审结果
            if benti_data:
                data = benti_data[0]
                for key, value in data.items():
                    if isinstance(value, (int, float)) and key not in ['序号']:
                        is_abnormal = value > 80  # 示例：值大于80视为异常
                        # 生成随机的历史指标值区间
                        min_value = 25
                        max_value = 45
                        review_data['benti'][key] = {
                            'value': value,
                            'status': '异常' if is_abnormal else '正常',
                            'detail': f'指标值偏离历史均值较大：历史指标值区间为【{min_value}-{max_value}】，现指标值为{value}，异常。' if is_abnormal else f'指标值在合理范围内：历史指标值区间为【{min_value}-{max_value}】，现指标值为{value}，正常。'
                        }
            
            # 处理其他费用指标校审结果
            if qita_data:
                data = qita_data[0]
                for key, value in data.items():
                    if isinstance(value, (int, float)) and key not in ['序号']:
                        is_abnormal = value > 80  # 示例：值大于80视为异常
                        # 生成随机的历史指标值区间
                        min_value = 25
                        max_value = 45
                        review_data['qita'][key] = {
                            'value': value,
                            'status': '异常' if is_abnormal else '正常',
                            'detail': f'指标值偏离历史均值较大：历史指标值区间为【{min_value}-{max_value}】，现指标值为{value}，异常。' if is_abnormal else f'指标值在合理范围内：历史指标值区间为【{min_value}-{max_value}】，现指标值为{value}，正常。'
                        }
            
            return jsonify({
                "message": "指标校审成功",
                "status": "success",
                "review_data": review_data
            })
        else:
            return jsonify({"error": "保存数据失败"}), 500
            
    except Exception as e:
        return jsonify({"error": str(e)}), 500

# 添加数据文件访问路由
@app.route('/data/<path:filename>')
def serve_data(filename):
    return send_from_directory('data', filename)

# 404页面
@app.errorhandler(404)
def page_not_found(e):
    return render_template('404.html'), 404

# API: 获取组价工程列表
@app.route('/api/pricing/projects', methods=['GET'])
def get_pricing_projects():
    try:
        data = load_json_data(PRICING_PROJECTS_FILE)
        projects = data.get('projects', [])
        
        # 按创建时间倒序排序
        projects.sort(key=lambda x: x.get('创建时间', ''), reverse=True)
        
        return jsonify({"projects": projects})
    except Exception as e:
        print(f"获取组价工程列表失败: {str(e)}")
        return jsonify({"error": str(e)}), 500

# API: 为旧页面提供组价工程数据
@app.route('/api/pricing/projects/old', methods=['GET'])
def get_pricing_projects_old():
    try:
        data = load_json_data(PRICING_PROJECTS_FILE)
        projects = data.get('projects', [])
        
        # 按创建时间倒序排序
        projects.sort(key=lambda x: x.get('创建时间', ''), reverse=True)
        
        # 旧页面直接需要项目数组而不是包含在projects字段中
        return jsonify(projects)
    except Exception as e:
        print(f"获取旧页面组价工程列表失败: {str(e)}")
        return jsonify({"error": str(e)}), 500

# API: 为旧页面提供工程搜索功能
@app.route('/api/pricing/projects/search', methods=['GET'])
def search_pricing_projects():
    try:
        data = load_json_data(PRICING_PROJECTS_FILE)
        projects = data.get('projects', [])
        
        # 获取搜索参数
        project_name = request.args.get('工程名称', '')
        voltage_level = request.args.get('电压等级', '')
        line_length = request.args.get('线路总长度', '')
        
        # 过滤结果
        filtered_projects = []
        for project in projects:
            if (project_name and project_name not in project.get('工程名称', '')):
                continue
            if (voltage_level and voltage_level != project.get('电压等级', '')):
                continue
            if (line_length and float(line_length) != float(project.get('线路总长度', 0))):
                continue
            filtered_projects.append(project)
        
        # 按创建时间倒序排序
        filtered_projects.sort(key=lambda x: x.get('创建时间', ''), reverse=True)
        
        return jsonify(filtered_projects)
    except Exception as e:
        print(f"搜索组价工程失败: {str(e)}")
        return jsonify({"error": str(e)}), 500

# API: 创建新组价工程
@app.route('/api/pricing/projects', methods=['POST'])
def create_pricing_project():
    try:
        data = load_json_data(PRICING_PROJECTS_FILE)
        projects = data.get('projects', [])
        new_project = request.json
        
        # 生成新序号
        max_seq = max((p.get('序号', 0) for p in projects), default=0)
        new_project['序号'] = max_seq + 1
        
        # 设置初始状态
        new_project['特征段数量'] = 0
        new_project['组价状态'] = '未组价'
        new_project['创建时间'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        new_project['组价时间'] = ''
        
        # 添加到列表
        projects.append(new_project)
        data['projects'] = projects
        
        # 保存数据
        if save_json_data(PRICING_PROJECTS_FILE, data):
            return jsonify({"message": "工程创建成功", "project": new_project}), 201
        else:
            return jsonify({"error": "保存数据失败"}), 500
            
    except Exception as e:
        print(f"创建组价工程失败: {str(e)}")
        return jsonify({"error": str(e)}), 500

# API: 更新组价工程状态
@app.route('/api/pricing/projects/<int:project_id>', methods=['PATCH'])
def update_pricing_project(project_id):
    try:
        data = load_json_data(PRICING_PROJECTS_FILE)
        projects = data.get('projects', [])
        updates = request.json
        
        # 查找并更新项目
        for project in projects:
            if project['序号'] == project_id:
                # 允许更新的字段
                allowed_fields = ['组价状态', '特征段数量', '组价时间']
                for field in allowed_fields:
                    if field in updates:
                        project[field] = updates[field]
                
                # 如果状态更新为已组价，自动添加组价时间
                if updates.get('组价状态') == '已组价':
                    project['组价时间'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                
                data['projects'] = projects
                if save_json_data(PRICING_PROJECTS_FILE, data):
                    return jsonify({"message": "工程更新成功", "project": project})
                else:
                    return jsonify({"error": "保存数据失败"}), 500
                    
        return jsonify({"error": "工程不存在"}), 404
            
    except Exception as e:
        print(f"更新组价工程失败: {str(e)}")
        return jsonify({"error": str(e)}), 500

# 快速组价主页面 - 新版本（工作区）
@app.route('/quick_pricing/workspace')
def quick_pricing_workspace():
    return render_template('quick_pricing/quickPricingWorkspace.html')

# 快速组价主页面 - 旧版本（兼容性保留）
@app.route('/quick_pricing/quickPricing')
def quick_pricing_main():
    return render_template('quick_pricing/old/quickPricing.html')

# 旧页面，获取特征段
# 以下路由保留用于兼容性
@app.route('/quick_pricing/feature_sections/<int:project_id>')
def feature_sections(project_id):
    return render_template('quick_pricing/old/featureSection.html', project_id=project_id)

@app.route('/quick_pricing/indicator_select/<int:project_id>/<int:section_id>')
def indicator_select(project_id, section_id):
    return render_template('quick_pricing/section_pricingSum.html', project_id=project_id, section_id=section_id)

# 新页面，获取特征段
# API: 获取工程特征段列表
@app.route('/api/pricing/projects/<int:project_id>/feature_sections', methods=['GET'])
def get_project_sections(project_id):
    """获取工程的特征段列表"""
    app.logger.info(f"获取工程ID {project_id} 的特征段列表")
    
    try:
        # 从JSON文件读取特征段数据
        with open('data/feature_sections.json', 'r', encoding='utf-8') as file:
            data = json.load(file)
            all_sections = data.get('feature_sections', [])
        
        # 从JSON文件读取工程数据，用于获取工程名称
        projects_data = load_json_data(PROJECT_DATA_FILE)
        
        # 查找当前工程
        current_project = None
        for project in projects_data:
            if project.get('序号') == project_id:
                current_project = project
                break
        
        # 获取工程名称
        project_name = current_project.get('工程名称', '') if current_project else ''
        
        # 过滤出当前工程的特征段，并添加工程名称
        project_sections = []
        for section in all_sections:
            if section.get('工程序号') == project_id:
                # 创建特征段的副本，避免修改原始数据
                section_copy = section.copy()
                # 添加工程名称
                section_copy['工程名称'] = project_name
                project_sections.append(section_copy)
        
        app.logger.info(f"找到 {len(project_sections)} 个特征段，工程ID: {project_id}")
        app.logger.debug(f"特征段数据: {project_sections}")
        return jsonify(project_sections)
    except Exception as e:
        app.logger.error(f"获取特征段列表失败: {str(e)}")
        return jsonify({"error": str(e)}), 500

# API: 创建新特征段
@app.route('/api/pricing/projects/<int:project_id>/feature_sections', methods=['POST'])
def create_feature_section(project_id):
    try:
        # 加载特征段数据
        sections_data = load_json_data(FEATURE_SECTIONS_FILE)
        sections = sections_data.get('feature_sections', [])
        
        # 加载工程数据以更新特征段数量
        projects_data = load_json_data(PRICING_PROJECTS_FILE)
        projects = projects_data.get('projects', [])
        
        new_section = request.json
        
        # 生成新序号
        max_seq = max((s.get('序号', 0) for s in sections), default=0)
        new_section['序号'] = max_seq + 1
        new_section['工程序号'] = project_id
        
        # 设置初始状态
        new_section['指标选择状态'] = '未选择'
        new_section['创建时间'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        # 添加到特征段列表
        sections.append(new_section)
        sections_data['feature_sections'] = sections
        
        # 更新工程的特征段数量
        for project in projects:
            if project['序号'] == project_id:
                project['特征段数量'] = len([s for s in sections if s['工程序号'] == project_id])
                break
        
        # 保存数据
        if (save_json_data(FEATURE_SECTIONS_FILE, sections_data) and 
            save_json_data(PRICING_PROJECTS_FILE, projects_data)):
            return jsonify({"message": "特征段创建成功", "section": new_section}), 201
        else:
            return jsonify({"error": "保存数据失败"}), 500
            
    except Exception as e:
        print(f"创建特征段失败: {str(e)}")
        return jsonify({"error": str(e)}), 500

# API: 更新特征段状态
@app.route('/api/pricing/projects/<int:project_id>/feature_sections/<int:section_id>', methods=['PATCH'])
def update_feature_section(project_id, section_id):
    try:
        data = load_json_data(FEATURE_SECTIONS_FILE)
        sections = data.get('feature_sections', [])
        updates = request.json
        
        # 查找并更新特征段
        for section in sections:
            if section['序号'] == section_id and section['工程序号'] == project_id:
                # 允许更新的字段
                allowed_fields = ['指标选择状态', '本体指标序号', '其他指标序号', '备注', '指标计算状态']
                for field in allowed_fields:
                    if field in updates:
                        section[field] = updates[field]
                
                data['feature_sections'] = sections
                if save_json_data(FEATURE_SECTIONS_FILE, data):
                    return jsonify({"message": "特征段更新成功", "section": section})
                else:
                    return jsonify({"error": "保存数据失败"}), 500
                    
        return jsonify({"error": "特征段不存在"}), 404
            
    except Exception as e:
        print(f"更新特征段失败: {str(e)}")
        return jsonify({"error": str(e)}), 500

# API: 删除特征段
@app.route('/api/pricing/projects/<int:project_id>/feature_sections/<int:section_id>', methods=['DELETE'])
def delete_feature_section(project_id, section_id):
    try:
        # 加载特征段数据
        sections_data = load_json_data(FEATURE_SECTIONS_FILE)
        sections = sections_data.get('feature_sections', [])
        
        # 加载工程数据以更新特征段数量
        projects_data = load_json_data(PRICING_PROJECTS_FILE)
        projects = projects_data.get('projects', [])
        
        # 查找并删除特征段
        for i, section in enumerate(sections):
            if section['序号'] == section_id and section['工程序号'] == project_id:
                sections.pop(i)
                
                # 更新工程的特征段数量
                for project in projects:
                    if project['序号'] == project_id:
                        project['特征段数量'] = len([s for s in sections if s['工程序号'] == project_id])
                        break
                
                # 保存数据
                sections_data['feature_sections'] = sections
                if (save_json_data(FEATURE_SECTIONS_FILE, sections_data) and 
                    save_json_data(PRICING_PROJECTS_FILE, projects_data)):
                    return jsonify({"message": "特征段删除成功"})
                else:
                    return jsonify({"error": "保存数据失败"}), 500
                    
        return jsonify({"error": "特征段不存在"}), 404
            
    except Exception as e:
        print(f"删除特征段失败: {str(e)}")
        return jsonify({"error": str(e)}), 500

# API: 获取特征段详情
@app.route('/api/pricing/sections/<int:section_id>', methods=['GET'])
def get_section_detail(section_id):
    try:
        data = load_json_data(FEATURE_SECTIONS_FILE)
        sections = data.get('sections', [])
        
        # 查找特征段
        section = next((s for s in sections if s.get('序号') == section_id), None)
        if not section:
            return jsonify({"error": "特征段不存在"}), 404
            
        return jsonify(section)
    except Exception as e:
        print(f"获取特征段详情失败: {str(e)}")
        return jsonify({"error": str(e)}), 500

# API: 查询匹配的本体费用指标
@app.route('/api/pricing/indicators/benti', methods=['GET'])
def query_benti_indicators():
    try:
        # 获取查询参数
        wind_speed = float(request.args.get('wind_speed', 0))
        ice_thickness = float(request.args.get('ice_thickness', 0))
        circuit_count = request.args.get('circuit_count', '')
        wire_type = request.args.get('wire_type', '')
        
        # 加载本体费用指标数据
        data = load_json_data(BENTI_DATA_FILE)
        indicators = data.get('indicators', [])
        
        # 根据边界条件筛选指标
        matched = []
        for indicator in indicators:
            if (abs(indicator.get('风速', 0) - wind_speed) <= 2 and  # 风速误差在2m/s以内
                abs(indicator.get('覆冰', 0) - ice_thickness) <= 2 and  # 覆冰误差在2mm以内
                indicator.get('回路数', '') == circuit_count and  # 回路数完全匹配
                indicator.get('导线型号', '').lower() == wire_type.lower()):  # 导线型号忽略大小写匹配
                matched.append(indicator)
        
        # 按匹配度排序（这里可以添加更复杂的排序逻辑）
        matched.sort(key=lambda x: (
            abs(x.get('风速', 0) - wind_speed) + 
            abs(x.get('覆冰', 0) - ice_thickness)
        ))
        
        return jsonify(matched)
    except Exception as e:
        print(f"查询本体费用指标失败: {str(e)}")
        return jsonify({"error": str(e)}), 500

# API: 查询匹配的其他费用指标
@app.route('/api/pricing/indicators/qita', methods=['GET'])
def query_qita_indicators():
    try:
        wind_speed = float(request.args.get('wind_speed', 0))
        ice_thickness = float(request.args.get('ice_thickness', 0))
        circuit_count = request.args.get('circuit_count')
        
        # 从数据文件中读取其他费用指标
        qita_data = load_json_data(QITA_DATA_FILE)
        
        # 根据条件筛选指标
        matched = sorted(
            [ind for ind in qita_data if str(ind.get('回路数')) == str(circuit_count)],
            key=lambda x: (
                abs(x.get('风速', 0) - wind_speed) +
                abs(x.get('覆冰', 0) - ice_thickness)
            )
        )
        
        return jsonify(matched)
    except Exception as e:
        print(f"查询其他费用指标失败: {str(e)}")
        return jsonify({"error": str(e)}), 500


# API: 获取工程详情（包含基本信息和特征段列表）
@app.route('/api/pricing/projects/<int:project_id>/detail', methods=['GET'])
def get_project_detail(project_id):
    try:
        # 加载工程数据
        projects_data = load_json_data(PRICING_PROJECTS_FILE)
        projects = projects_data.get('projects', [])
        
        # 查找工程
        project = next((p for p in projects if p.get('序号') == project_id), None)
        if not project:
            return jsonify({"error": "工程不存在"}), 404
            
        # 加载特征段数据
        sections_data = load_json_data(FEATURE_SECTIONS_FILE)
        sections = sections_data.get('sections', [])
        
        # 获取工程的特征段
        project_sections = [s for s in sections if s.get('工程序号') == project_id]
        
        # 加载本体费用指标数据
        benti_data = load_json_data(BENTI_DATA_FILE)
        benti_indicators = benti_data.get('indicators', [])
        
        # 加载其他费用指标数据
        qita_data = load_json_data(QITA_DATA_FILE)
        qita_indicators = qita_data.get('indicators', [])
        
        # 计算每个特征段的组价结果
        for section in project_sections:
            # 获取选择的指标
            benti_indicator = next((i for i in benti_indicators if i.get('序号') == section.get('本体指标序号')), None)
            qita_indicator = next((i for i in qita_indicators if i.get('序号') == section.get('其他指标序号')), None)
            
            # 计算特征段长度
            section_length = section.get('结束里程', 0) - section.get('起始里程', 0)
            
            # 计算本体费用
            if benti_indicator:
                section['本体费用'] = {
                    '单公里造价': benti_indicator.get('单公里造价', 0),
                    '总造价': round(benti_indicator.get('单公里造价', 0) * section_length, 2)
                }
            
            # 计算其他费用
            if qita_indicator:
                section['其他费用'] = {
                    '单公里造价': qita_indicator.get('单公里造价', 0),
                    '总造价': round(qita_indicator.get('单公里造价', 0) * section_length, 2)
                }
            
            # 计算特征段总造价
            section['特征段总造价'] = round(
                section.get('本体费用', {}).get('总造价', 0) +
                section.get('其他费用', {}).get('总造价', 0),
                2
            )
        
        # 计算工程总造价
        project['工程总造价'] = round(sum(s.get('特征段总造价', 0) for s in project_sections), 2)
        
        # 保存组价结果
        pricing_results_data = load_json_data(PRICING_RESULTS_FILE)
        pricing_results = pricing_results_data.get('results', [])
        
        # 查找或创建工程的组价结果
        result = next((r for r in pricing_results if r.get('工程序号') == project_id), None)
        if result:
            result.update({
                '特征段结果': project_sections,
                '工程总造价': project['工程总造价'],
                '组价时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            })
        else:
            pricing_results.append({
                '工程序号': project_id,
                '特征段结果': project_sections,
                '工程总造价': project['工程总造价'],
                '组价时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            })
        
        pricing_results_data['results'] = pricing_results
        save_json_data(PRICING_RESULTS_FILE, pricing_results_data)
        
        return jsonify({
            'project': project,
            'sections': project_sections
        })
    except Exception as e:
        print(f"获取工程详情失败: {str(e)}")
        return jsonify({"error": str(e)}), 500

# API: 导出组价结果
@app.route('/api/pricing/projects/<int:project_id>/export', methods=['GET'])
def export_pricing_result(project_id):
    try:
        # 获取工程详情
        response = get_project_detail(project_id)
        if response.status_code != 200:
            return response
            
        data = response.get_json()
        project = data.get('project')
        sections = data.get('sections')
        
        # 生成Excel文件
        output = BytesIO()
        workbook = xlsxwriter.Workbook(output)
        
        # 添加工程概况工作表
        overview_sheet = workbook.add_worksheet('工程概况')
        overview_sheet.write(0, 0, '工程名称')
        overview_sheet.write(0, 1, project.get('工程名称'))
        overview_sheet.write(1, 0, '电压等级')
        overview_sheet.write(1, 1, project.get('电压等级'))
        overview_sheet.write(2, 0, '线路总长度(km)')
        overview_sheet.write(2, 1, project.get('线路总长度'))
        overview_sheet.write(3, 0, '特征段数量')
        overview_sheet.write(3, 1, project.get('特征段数量'))
        overview_sheet.write(4, 0, '工程总造价(万元)')
        overview_sheet.write(4, 1, project.get('工程总造价'))
        overview_sheet.write(5, 0, '组价时间')
        overview_sheet.write(5, 1, project.get('组价时间'))
        
        # 添加特征段明细工作表
        detail_sheet = workbook.add_worksheet('特征段明细')
        headers = ['特征段名称', '线路长度(km)', '风速(m/s)', '覆冰(mm)', 
                  '回路数', '导线规格', '本体费用单价', '本体费用合计', 
                  '其他费用单价', '其他费用合计', '特征段总造价']
        for col, header in enumerate(headers):
            detail_sheet.write(0, col, header)
        
        for row, section in enumerate(sections, start=1):
            detail_sheet.write(row, 0, section.get('特征段名称'))
            detail_sheet.write(row, 1, section.get('线路长度'))
            detail_sheet.write(row, 2, section.get('边界条件', {}).get('风速'))
            detail_sheet.write(row, 3, section.get('边界条件', {}).get('覆冰'))
            detail_sheet.write(row, 4, section.get('边界条件', {}).get('回路数'))
            detail_sheet.write(row, 5, section.get('边界条件', {}).get('导线规格'))
            detail_sheet.write(row, 6, section.get('本体费用', {}).get('单公里造价'))
            detail_sheet.write(row, 7, section.get('本体费用', {}).get('总造价'))
            detail_sheet.write(row, 8, section.get('其他费用', {}).get('单公里造价'))
            detail_sheet.write(row, 9, section.get('其他费用', {}).get('总造价'))
            detail_sheet.write(row, 10, section.get('特征段总造价'))
        
        workbook.close()
        output.seek(0)
        
        return send_file(
            output,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name=f"{project.get('工程名称')}_组价结果.xlsx"
        )
    except Exception as e:
        print(f"导出组价结果失败: {str(e)}")
        return jsonify({"error": str(e)}), 500

@app.route('/quick_pricing/section_management/<int:project_id>')
def section_management(project_id):
    return render_template('quick_pricing/section_management.html', project_id=project_id)

# 修改原有路由为内容路由
@app.route('/quick_pricing/indicator_select_content/<int:project_id>/<int:section_id>')
def indicator_select_content(project_id, section_id):
    """渲染历史指标匹配页面内容"""
    return render_template('quick_pricing/section_pricingSum.html')

# 新路由：取代原 indicator_select_content
@app.route('/quick_pricing/section_pricingSum_content/<int:project_id>/<int:section_id>')
def section_pricingSum_content(project_id, section_id):
    """渲染特征段组价页面内容（替代 indicator_select_content）"""
    return render_template('quick_pricing/section_pricingSum.html')

# --- 兼容旧路径，可在确认无使用后删除 ---

@app.route('/api/pricing/projects/<int:project_id>/feature_sections/<int:section_id>')
def get_feature_section(project_id, section_id):
    """获取特定特征段的详细信息"""
    try:
        # 加载特征段数据
        feature_sections = load_json_data(FEATURE_SECTIONS_FILE)
        
        # 查找指定的特征段
        section = next((s for s in feature_sections.get('feature_sections', [])
                       if s.get('序号') == section_id and s.get('工程序号') == project_id), None)
        
        if section:
            return jsonify(section)
        else:
            return jsonify({'error': '特征段不存在'}), 404
            
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/pricing/projects/<int:project_id>/feature_sections/<int:section_id>/indicators', methods=['POST'])
def save_section_indicators_v2(project_id, section_id):
    """
    保存特征段的指标选择（新版本）
    
    Args:
        project_id: 工程ID
        section_id: 特征段ID
        
    Returns:
        成功：{'message': '指标选择保存成功'}
        失败：{'error': 错误信息}, 500
    
    Request Body:
        {
            "本体指标序号": int,
            "其他指标序号": int
        }
    """
    try:
        data = request.json
        feature_sections = load_json_data(FEATURE_SECTIONS_FILE)
        
        # 查找并更新特征段
        for section in feature_sections.get('feature_sections', []):
            if section.get('序号') == section_id and section.get('工程序号') == project_id:
                section['本体指标序号'] = data.get('本体指标序号')
                section['其他指标序号'] = data.get('其他指标序号')
                section['指标选择状态'] = '已选择'
                break
        
        # 保存更新后的数据
        if save_json_data(FEATURE_SECTIONS_FILE, feature_sections):
            return jsonify({'message': '指标选择保存成功'})
        else:
            return jsonify({'error': '保存数据失败'}), 500
            
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# 删除工程
@app.route('/api/pricing/projects/<int:project_id>', methods=['DELETE'])
def delete_pricing_project(project_id):
    try:
        # 加载工程数据
        data = load_json_data(PRICING_PROJECTS_FILE)
        projects = data.get('projects', [])
        
        # 查找要删除的工程
        project_index = None
        for i, project in enumerate(projects):
            if project.get('序号') == project_id:
                project_index = i
                break
        
        if project_index is None:
            return jsonify({'message': '工程不存在'}), 404
            
        # 删除工程
        deleted_project = projects.pop(project_index)
        
        # 保存更新后的数据
        if save_json_data(PRICING_PROJECTS_FILE, {'projects': projects}):
            # 同时删除该工程的特征段数据
            sections_data = load_json_data(FEATURE_SECTIONS_FILE)
            sections_data['feature_sections'] = [
                section for section in sections_data.get('feature_sections', [])
                if section.get('project_id') != project_id
            ]
            save_json_data(FEATURE_SECTIONS_FILE, sections_data)
            
            return jsonify({'message': '工程删除成功', 'deleted': deleted_project}), 200
        else:
            return jsonify({'message': '保存数据失败'}), 500
            
    except Exception as e:
        print(f"删除工程失败: {str(e)}")
        return jsonify({'message': f'删除工程失败: {str(e)}'}), 500

# API: 工程指标量汇总
@app.route('/api/pricing/projects/<int:project_id>/indicators/summary', methods=['POST'])
def summarize_project_indicators(project_id):
    """
    汇总工程下所有特征段的指标量
    
    Args:
        project_id: 工程ID
        
    Returns:
        成功：{'message': '指标量汇总成功'}
        失败：{'error': 错误信息}, 500
    """
    try:
        # 加载工程数据
        projects_data = load_json_data(PRICING_PROJECTS_FILE)
        projects = projects_data.get('projects', [])
        
        # 加载特征段数据
        sections_data = load_json_data(FEATURE_SECTIONS_FILE)
        sections = sections_data.get('feature_sections', [])
        
        # 查找工程
        project = next((p for p in projects if p.get('序号') == project_id), None)
        if not project:
            return jsonify({'error': '工程不存在'}), 404
            
        # 获取工程下的所有特征段
        project_sections = [s for s in sections if s.get('工程序号') == project_id]
        
        # 检查是否所有特征段都已计算
        uncalculated_sections = [s for s in project_sections if s.get('指标计算状态') != '已计算']
        if uncalculated_sections:
            return jsonify({'error': '存在未计算的特征段，请先完成所有特征段的指标计算'}), 400
            
        # 更新工程的指标汇总状态
        project['指标汇总状态'] = '已汇总'
        project['指标汇总时间'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        # 保存更新后的数据
        if save_json_data(PRICING_PROJECTS_FILE, projects_data):
            return jsonify({'message': '指标量汇总成功'})
        else:
            return jsonify({'error': '保存数据失败'}), 500
            
    except Exception as e:
        print(f"指标量汇总失败: {str(e)}")
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    app.run(debug=True) 