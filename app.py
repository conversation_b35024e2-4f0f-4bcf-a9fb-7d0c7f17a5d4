import os
import json
from flask import Flask, render_template, send_from_directory

app = Flask(__name__, static_folder='static')

# 数据文件路径配置
DATA_DIR = 'data'

# -------- 通用工具函数 --------

def load_json_data(file_path):
    """通用JSON数据加载函数"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        return []
    except Exception as e:
        print(f"加载数据失败 {file_path}: {e}")
        return []


def save_json_data(file_path, data):
    """通用JSON数据保存函数"""
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=4)
        return True
    except Exception as e:
        print(f"保存数据失败 {file_path}: {e}")
        return False

# -------- 注册业务蓝图 --------
from modules.his_ind import bp as his_ind_bp
from modules.audit import bp as audit_bp
from modules.quick_pricing import bp as quick_pricing_bp

app.register_blueprint(his_ind_bp)
app.register_blueprint(audit_bp)
app.register_blueprint(quick_pricing_bp)

# -------- 公共页面与服务 --------

@app.route('/')
def index():
    return render_template('index.html')


@app.route('/settings.html')
def settings():
    return render_template('settings/settings.html')


@app.route('/data/<path:filename>')
def serve_data(filename):
    """静态 JSON、Excel 等数据文件访问"""
    return send_from_directory(DATA_DIR, filename)


@app.errorhandler(404)
def page_not_found(e):
    return render_template('404.html'), 404 