{% extends "base.html" %}

{% block title %}历史指标库 - 电网线路工程造价分析平台{% endblock %}

{% block head %}
<style>
    /* 覆盖全局容器样式 */
    main {
        padding: 0;
        min-height: calc(100vh - 120px);
    }

    main .container {
        max-width: none;
        padding: 0;
        height: 100%;
    }

    .query-form {
        display: flex;
        flex-wrap: nowrap;
        gap: 1rem;
        margin-bottom: 1rem;
        align-items: flex-end;
        padding: 1rem;
    }
    
    .form-group {
        display: flex;
        flex-direction: column;
        flex: 0 0 150px;
    }
    
    .form-group label {
        margin-bottom: 0.5rem;
        color: var(--text-color);
        white-space: nowrap;
    }
    
    .form-group input,
    .form-group select {
        padding: 0.5rem;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 1rem;
        width: 100%;
    }

    .button-group {
        display: flex;
        gap: 1rem;
        margin-left: auto;
        white-space: nowrap;
    }
    
    .data-table {
        width: 100%;
        border-collapse: collapse;
        font-size: 0.9rem;
    }
    
    .data-table th,
    .data-table td {
        padding: 0.75rem 1rem;
        text-align: left;
        border: 1px solid #ddd;
        white-space: nowrap;
    }
    
    .data-table th {
        background-color: #f5f5f5;
        font-weight: 500;
        position: sticky;
        top: 0;
        z-index: 1;
    }
    
    .data-table tbody tr:hover {
        background-color: #f5f5f5;
    }
    
    .table-container {
        overflow: auto;
        border: 1px solid #ddd;
        border-radius: 4px;
        height: calc(100vh - 280px);
        margin: 0 1rem 0.5rem;
    }

    .tab-container {
        height: 100%;
        display: flex;
        flex-direction: column;
    }

    .tab-buttons {
        display: flex;
        gap: 1rem;
        padding: 1rem 1rem 0;
        border-bottom: 1px solid #ddd;
    }

    .tab-button {
        padding: 0.75rem 1.5rem;
        border: none;
        background: none;
        cursor: pointer;
        font-size: 1rem;
        border-bottom: 2px solid transparent;
        transition: all 0.3s;
    }

    .tab-button.active {
        border-bottom: 2px solid #1890ff;
        color: #1890ff;
    }

    .tab-content {
        display: none;
        flex: 1;
        height: 100%;
    }

    .tab-content.active {
        display: flex;
        flex-direction: column;
    }

    .pagination {
        display: flex;
        justify-content: center;
        gap: 0.5rem;
        padding: 0.5rem;
        background: white;
        border-top: 1px solid #ddd;
        margin: 0 1rem;
    }

    .pagination button {
        padding: 0.35rem 0.75rem;
        border: 1px solid #ddd;
        background: white;
        border-radius: 4px;
        cursor: pointer;
        transition: all 0.3s;
        font-size: 0.9rem;
    }

    .pagination button:hover {
        background: #f5f5f5;
    }

    .pagination button.active {
        background: #1890ff;
        color: white;
        border-color: #1890ff;
    }

    .hidden-data {
        display: none;
    }

    .modal-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.4);
        display: none;
        align-items: center;
        justify-content: center;
        z-index: 9999;
    }

    .modal-overlay .modal {
        background: #fff;
        padding: 1.5rem;
        border-radius: 8px;
        max-width: 420px;
        width: 90%;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    }

    .modal-overlay h3 {
        margin-top: 0;
        margin-bottom: 0.75rem;
    }

    .modal-overlay p {
        margin: 0.5rem 0 1rem;
        font-size: 0.9rem;
    }

    .modal-actions {
        display: flex;
        justify-content: flex-end;
        gap: 0.5rem;
    }
</style>
{% endblock %}

{% block content %}
<!-- 隐藏的数据元素 -->
<div id="benti-data" class="hidden-data">{{ benti_data|tojson|safe }}</div>
<div id="qita-data" class="hidden-data">{{ qita_data|tojson|safe }}</div>

<div class="tab-container">
    <div class="tab-buttons">
        <button class="tab-button active" onclick="switchTab('tab1')">本体费用指标数据查询</button>
        <button class="tab-button" onclick="switchTab('tab2')">其他费用指标数据查询</button>
    </div>

    <!-- 本体费用指标数据查询 -->
    <div id="tab1" class="tab-content active">
        <form class="query-form">
            <div class="form-group">
                <label>工程名称</label>
                <input type="text" placeholder="请输入工程名称" id="projectNameInput">
            </div>
            <div class="form-group">
                <label>回路数</label>
                <select id="loopSelect">
                    <option>全部</option>
                    <option>单回路</option>
                    <option>双回路</option>
                    <option>四回路</option>
                </select>
            </div>
            <div class="form-group">
                <label>风速(m/s)</label>
                <input type="number" placeholder="如 27" id="windInput">
            </div>
            <div class="form-group">
                <label>覆冰(mm)</label>
                <input type="number" placeholder="如 5" id="iceInput">
            </div>
            <div class="form-group">
                <label>导线规格</label>
                <input type="text" placeholder="请输入导线规格" id="specInput">
            </div>
            <div class="button-group">
                <button type="button" class="btn btn-primary" id="bentiQueryBtn">查询</button>
                <button type="button" class="btn btn-secondary" id="bentiExportBtn">导出</button>
                <button type="button" class="btn btn-secondary" id="bentiImportBtn">导入</button>
            </div>
        </form>
        <div class="table-container">
            <table class="data-table" id="bentiTable">
                <thead>
                    <tr>
                        <th>序号</th>
                        <th>工程名称</th>
                        <th>线路工程</th>
                        <th>线路总长度(km)</th>
                        <th>回路数</th>
                        <th>风速(m/s)</th>
                        <th>覆冰(mm)</th>
                        <th>导线规格</th>
                        <th>平地(%)</th>
                        <th>丘陵(%)</th>
                        <th>山地(%)</th>
                        <th>高山(%)</th>
                        <th>峻岭(%)</th>
                        <th>泥沼(%)</th>
                        <th>河网(%)</th>
                        <th>人力运距(km)</th>
                        <th>汽车运距（含拖拉机）(km)</th>
                        <th>铁塔基数(基)</th>
                        <th>直线塔(基)</th>
                        <th>耐张塔(基)</th>
                        <th>耐张比例(%)</th>
                        <th>导线(t)</th>
                        <th>塔材(t)</th>
                        <th>基础钢材(t)</th>
                        <th>地脚螺栓和插入式角钢(t)</th>
                        <th>挂线金具(t)</th>
                        <th>导线间隔棒(套)</th>
                        <th>防振锤(个)</th>
                        <th>导线防振锤(个)</th>
                        <th>地线防振锤(个)</th>
                        <th>合成/复合绝缘子(支)</th>
                        <th>玻璃绝缘子/盘式绝缘子(支)</th>
                        <th>硬跳(套)</th>
                        <th>现浇混凝土(m³)</th>
                        <th>灌柱桩基础混凝土(m³)</th>
                        <th>基础护壁(m³)</th>
                        <th>基础垫层(m³)</th>
                        <th>钻孔灌注桩深度(m)</th>
                        <th>护坡、挡土墙(m³)</th>
                        <th>土方量(m³)</th>
                        <th>基坑土方（非机械）(m³)</th>
                        <th>基坑土方（机械）(m³)</th>
                        <th>接地槽(m³)</th>
                        <th>排水沟(m³)</th>
                        <th>尖峰、基面(m³)</th>
                        <th>本体工程(万元)</th>
                        <th>基础工程(万元)</th>
                        <th>杆塔工程(万元)</th>
                        <th>接地工程(万元)</th>
                        <th>架线工程(万元)</th>
                        <th>附件工程(万元)</th>
                        <th>辅助工程(万元)</th>
                    </tr>
                </thead>
                <tbody>
                </tbody>
            </table>
        </div>
        <div class="pagination">
            <button>1</button>
            <button>2</button>
            <button>3</button>
            <button>4</button>
            <button>5</button>
        </div>
    </div>

    <!-- 其他费用指标数据查询 -->
    <div id="tab2" class="tab-content">
        <form class="query-form">
            <div class="form-group">
                <label>工程名称</label>
                <input type="text" placeholder="请输入工程名称">
            </div>
            <div class="form-group">
                <label>线路工程</label>
                <input type="text" placeholder="请输入线路工程">
            </div>
            <div class="button-group">
                <button type="button" class="btn btn-primary">查询</button>
                <button type="button" class="btn btn-secondary">导出</button>
                <button type="button" class="btn btn-secondary">导入</button>
            </div>
        </form>
        <div class="table-container">
            <table class="data-table" id="qitaTable">
                <thead>
                    <tr>
                        <th>序号</th>
                        <th>工程名称</th>
                        <th>线路工程</th>
                        <th>合并气象区总长度</th>
                        <th>项目建设管理费（万元）</th>
                        <th>项目法人管理费（万元）</th>
                        <th>招标费（万元）</th>
                        <th>工程监理费（万元）</th>
                        <th>施工过程造价咨询及竣工结算审核费（万元）</th>
                        <th>工程保险费（万元）</th>
                        <th>项目建设技术服务费（万元）</th>
                        <th>项目前期工作费（万元）</th>
                        <th>勘察设计费（万元）</th>
                        <th>勘察费（万元）</th>
                        <th>设计费（万元）</th>
                        <th>基本设计费（万元）</th>
                        <th>其他设计费（万元）</th>
                        <th>设计文件评审费（万元）</th>
                        <th>可行性研究文件评审费（万元）</th>
                        <th>初步设计文件评审费（万元）</th>
                        <th>施工图文件评审费（万元）</th>
                        <th>工程建设检测费（万元）</th>
                        <th>电力工程质量检测费（万元）</th>
                        <th>桩基检测费（万元）</th>
                        <th>电力工程技术经济标准编制费（万元）</th>
                        <th>生产准备费（万元）</th>
                        <th>管理车辆购置费（万元）</th>
                        <th>工器具及办公家具购置费（万元）</th>
                        <th>生产职工培训及提前进场费（万元）</th>
                        <th>专业爆破服务费（万元）</th>
                    </tr>
                </thead>
                <tbody>
                </tbody>
            </table>
        </div>
        <div class="pagination">
            <button>1</button>
            <button>2</button>
            <button>3</button>
            <button>4</button>
            <button>5</button>
        </div>
    </div>
</div>

<div id="customModal" class="modal-overlay">
    <div class="modal">
        <h3 id="modalTitle"></h3>
        <p id="modalMessage"></p>
        <input type="file" id="modalFileInput" accept=".xlsx" style="display:none; margin-bottom:1rem;" />
        <div class="modal-actions">
            <button type="button" class="btn btn-secondary" id="modalCancelBtn">取消</button>
            <button type="button" class="btn btn-primary" id="modalConfirmBtn">确认</button>
        </div>
    </div>
</div>

<script>
// 从页面中的隐藏元素获取数据
const bentiData = JSON.parse(document.getElementById('benti-data').textContent);
const qitaData = JSON.parse(document.getElementById('qita-data').textContent);

function renderTable(data, tableId, startIndex = 0, pageSize = 10) {
    const tbody = document.querySelector(`#${tableId} tbody`);
    tbody.innerHTML = '';
    
    const endIndex = Math.min(startIndex + pageSize, data.length);
    const pageData = data.slice(startIndex, endIndex);
    
    pageData.forEach(item => {
        const tr = document.createElement('tr');
        
        // 根据表格类型选择对应的列
        const columns = tableId === 'bentiTable' ? [
            "序号", "工程名称", "线路工程", "线路总长度(km)", "回路数", "风速(m/s)", "覆冰(mm)",
            "导线规格", "平地(%)", "丘陵(%)", "山地(%)", "高山(%)", "峻岭(%)", "泥沼(%)", 
            "河网(%)", "人力运距(km)", "汽车运距（含拖拉机）(km)", "铁塔基数(基)", "直线塔(基)",
            "耐张塔(基)", "耐张比例(%)", "导线(t)", "塔材(t)", "基础钢材(t)", 
            "地脚螺栓和插入式角钢(t)", "挂线金具(t)", "导线间隔棒(套)", "防振锤(个)", 
            "导线防振锤(个)", "地线防振锤(个)", "合成/复合绝缘子(支)", 
            "玻璃绝缘子/盘式绝缘子(支)", "硬跳(套)", "现浇混凝土(m³)", "灌柱桩基础混凝土(m³)",
            "基础护壁(m³)", "基础垫层(m³)", "钻孔灌注桩深度(m)", "护坡、挡土墙(m³)", 
            "土方量(m³)", "基坑土方（非机械）(m³)", "基坑土方（机械）(m³)", "接地槽(m³)",
            "排水沟(m³)", "尖峰、基面(m³)", "本体工程(万元)", "基础工程(万元)", 
            "杆塔工程(万元)", "接地工程(万元)", "架线工程(万元)", "附件工程(万元)", 
            "辅助工程(万元)"
        ] : [
            "序号", "工程名称", "线路工程", "合并气象区总长度", "项目建设管理费（万元）",
            "项目法人管理费（万元）", "招标费（万元）", "工程监理费（万元）",
            "施工过程造价咨询及竣工结算审核费（万元）", "工程保险费（万元）",
            "项目建设技术服务费（万元）", "项目前期工作费（万元）", "勘察设计费（万元）",
            "勘察费（万元）", "设计费（万元）", "基本设计费（万元）", "其他设计费（万元）",
            "设计文件评审费（万元）", "可行性研究文件评审费（万元）", "初步设计文件评审费（万元）",
            "施工图文件评审费（万元）", "工程建设检测费（万元）", "电力工程质量检测费（万元）",
            "桩基检测费（万元）", "电力工程技术经济标准编制费（万元）", "生产准备费（万元）",
            "管理车辆购置费（万元）", "工器具及办公家具购置费（万元）",
            "生产职工培训及提前进场费（万元）", "专业爆破服务费（万元）"
        ];
        
        // 按照定义的列顺序添加数据
        columns.forEach(column => {
            const td = document.createElement('td');
            const value = item[column];
            td.textContent = (value === 0 || value) ? value : '';
            tr.appendChild(td);
        });
        
        tbody.appendChild(tr);
    });
}

document.addEventListener('DOMContentLoaded', function() {
    // 初始加载数据
    renderTable(bentiData, 'bentiTable');
    renderTable(qitaData, 'qitaTable');
    
    // 分页按钮点击事件
    document.querySelectorAll('.pagination').forEach(pagination => {
        pagination.addEventListener('click', function(e) {
            if (e.target.tagName === 'BUTTON') {
                const pageNum = parseInt(e.target.textContent) - 1;
                const tableId = this.closest('.tab-content').querySelector('.data-table').id;
                const data = tableId === 'bentiTable' ? bentiData : qitaData;
                renderTable(data, tableId, pageNum * 10);
                
                // 更新活动页码样式
                this.querySelectorAll('button').forEach(btn => btn.classList.remove('active'));
                e.target.classList.add('active');
            }
        });
    });
});

function switchTab(tabId) {
    // 隐藏所有tab内容
    document.querySelectorAll('.tab-content').forEach(tab => {
        tab.classList.remove('active');
    });
    
    // 移除所有tab按钮的active类
    document.querySelectorAll('.tab-button').forEach(button => {
        button.classList.remove('active');
    });
    
    // 显示选中的tab内容
    document.getElementById(tabId).classList.add('active');
    
    // 添加选中tab按钮的active类
    event.target.classList.add('active');
    
    // 重新加载当前tab的数据
    if (tabId === 'tab1') {
        renderTable(bentiData, 'bentiTable');
    } else {
        renderTable(qitaData, 'qitaTable');
    }
}

// 新增功能脚本
document.addEventListener('DOMContentLoaded', function () {
    const modalOverlay = document.getElementById('customModal');
    const modalTitle = document.getElementById('modalTitle');
    const modalMessage = document.getElementById('modalMessage');
    const modalFileInput = document.getElementById('modalFileInput');
    const modalCancelBtn = document.getElementById('modalCancelBtn');
    const modalConfirmBtn = document.getElementById('modalConfirmBtn');

    function showModal({ title, text, showFile = false, confirmText = '确认', cancelText = '取消', onConfirm = null }) {
        modalTitle.textContent = title;
        modalMessage.textContent = text;
        modalFileInput.style.display = showFile ? 'block' : 'none';
        if (showFile) modalFileInput.value = '';
        modalConfirmBtn.textContent = confirmText;
        modalCancelBtn.textContent = cancelText || '关闭';
        modalOverlay.style.display = 'flex';
        modalConfirmBtn.onclick = function () {
            if (onConfirm) onConfirm();
        };
        modalCancelBtn.onclick = hideModal;
    }

    function hideModal() {
        modalOverlay.style.display = 'none';
    }

    const queryBtn = document.getElementById('bentiQueryBtn');
    const exportBtn = document.getElementById('bentiExportBtn');
    const importBtn = document.getElementById('bentiImportBtn');

    // 查询按钮逻辑
    queryBtn.addEventListener('click', function () {
        // 动态过滤：仅对填写了值的字段进行比对，留空则不过滤
        const name = document.getElementById('projectNameInput').value.trim();
        const loopNum = document.getElementById('loopSelect').value;
        const wind = document.getElementById('windInput').value.trim();
        const ice = document.getElementById('iceInput').value.trim();
        const spec = document.getElementById('specInput').value.trim();

        const filtered = bentiData.filter(item => {
            if (name && (!item['工程名称'] || !item['工程名称'].includes(name))) return false;
            if (loopNum && loopNum !== '全部' && item['回路数'] !== loopNum) return false;
            if (wind && Number(item['风速(m/s)']) !== Number(wind)) return false;
            if (ice && Number(item['覆冰(mm)']) !== Number(ice)) return false;
            if (spec && (!item['导线规格'] || !item['导线规格'].includes(spec))) return false;
            return true;
        });
        renderTable(filtered, 'bentiTable');
    });

    // 导出按钮逻辑
    exportBtn.addEventListener('click', function () {
        showModal({
            title: '导出确认',
            text: '将所选择项目导出为excel文件，请确认是否导出。',
            confirmText: '导出',
            onConfirm: function () {
                showModal({
                    title: '提示',
                    text: '已导出成功！',
                    confirmText: '确定',
                    cancelText: null,
                    onConfirm: hideModal
                });
            }
        });
    });

    // 导入按钮逻辑
    importBtn.addEventListener('click', function () {
        showModal({
            title: '线路指标文件上传',
            text: '上传已确认的历史线路工程指标文件，导入将发布到历史指标库作为工程参考依据。',
            showFile: true,
            confirmText: '导入',
            onConfirm: function () {
                if (!modalFileInput.files.length) {
                    alert('请先选择要上传的文件！');
                    return;
                }
                showModal({
                    title: '确认导入',
                    text: '上传文件指标数据将作为发布历史指标库作为工程参考依据，请确认。',
                    confirmText: '确认',
                    onConfirm: function () {
                        // 添加演示数据记录
                        bentiData.push({
                            "序号": 1,
                            "工程名称": "500kV楚庭第二通道线路工程",
                            "线路工程": "500kV凤城至楚庭线路工程线路部分-新增",
                            "线路总长度(km)": 20,
                            "回路数": "双回路",
                            "风速(m/s)": 27,
                            "覆冰(mm)": 5,
                            "导线规格": "JL/LB20A 720/50"
                        });
                        renderTable(bentiData, 'bentiTable');
                        showModal({
                            title: '提示',
                            text: '导入成功！',
                            confirmText: '确定',
                            cancelText: null,
                            onConfirm: hideModal
                        });
                    }
                });
            }
        });
    });
});
</script>
{% endblock %}
