from flask import Blueprint, render_template, jsonify, request, send_file
from datetime import datetime
import os
import json
import io
import random
from docx import Document
from docx.shared import Pt, RGBColor
from docx.oxml.ns import qn

bp = Blueprint('audit', __name__)

# --- 常量与工具函数（复制自 app.py） ---
DATA_DIR = 'data'
BENTI_DATA_FILE = os.path.join(DATA_DIR, 'Ind_benti.json')
QITA_DATA_FILE = os.path.join(DATA_DIR, 'Ind_qita.json')
PROJECT_DATA_FILE = os.path.join(DATA_DIR, 'project_data.json')


def load_json_data(file_path):
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        if file_path == PROJECT_DATA_FILE:
            return []
        return []
    except Exception as e:
        print(f"加载数据失败 {file_path}: {e}")
        return []


def save_json_data(file_path, data):
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=4)
        return True
    except Exception as e:
        print(f"保存数据失败 {file_path}: {e}")
        return False


def load_project_data():
    return load_json_data(PROJECT_DATA_FILE)


def save_project_data(data):
    return save_json_data(PROJECT_DATA_FILE, data)

# --- 页面路由 ---

@bp.route('/audit')
def audit_index():
    return render_template('audit/auditIndex.html')


@bp.route('/audit/indicatorUpload.html')
def indicator_upload():
    return render_template('audit/indicatorUpload.html')

# 保留 ruleAudit 和 settings 页面逻辑在主应用，可按需迁移

# --- API: 指标校审预览 ---

@bp.route('/api/review/benti/<int:project_id>')
def get_benti_review(project_id):
    benti_data = load_json_data(BENTI_DATA_FILE)
    if benti_data:
        data = benti_data[0]
        review_data = {}
        for key, value in data.items():
            if isinstance(value, (int, float)) and key not in ['序号']:
                is_abnormal = value > 80
                min_value, max_value = 25, 45
                review_data[key] = {
                    'value': value,
                    'status': '异常' if is_abnormal else '正常',
                    'detail': (
                        f'指标值偏离历史均值较大：历史指标值区间为【{min_value}-{max_value}】，'
                        f'现指标值为{value}，异常。' if is_abnormal else
                        f'指标值在合理范围内：历史指标值区间为【{min_value}-{max_value}】，'
                        f'现指标值为{value}，正常。'
                    )
                }
        return jsonify(review_data)
    return jsonify({})


@bp.route('/api/review/qita/<int:project_id>')
def get_qita_review(project_id):
    qita_data = load_json_data(QITA_DATA_FILE)
    if qita_data:
        data = qita_data[0]
        review_data = {}
        for key, value in data.items():
            if isinstance(value, (int, float)) and key not in ['序号']:
                is_abnormal = value > 80
                min_value, max_value = 25, 45
                review_data[key] = {
                    'value': value,
                    'status': '异常' if is_abnormal else '正常',
                    'detail': (
                        f'指标值偏离历史均值较大：历史指标值区间为【{min_value}-{max_value}】，'
                        f'现指标值为{value}，异常。' if is_abnormal else
                        f'指标值在合理范围内：历史指标值区间为【{min_value}-{max_value}】，'
                        f'现指标值为{value}，正常。'
                    )
                }
        return jsonify(review_data)
    return jsonify({})

# --- API: 项目 CRUD 与状态更新 ---

@bp.route('/api/projects', methods=['GET'])
def get_projects():
    projects = load_project_data()
    sorted_projects = projects.copy()
    sorted_projects.sort(key=lambda x: x.get('创建时间', ''), reverse=True)
    return jsonify(sorted_projects)


@bp.route('/api/projects', methods=['POST'])
def create_project():
    try:
        projects = load_project_data()
        new_project = request.json
        if '序号' not in new_project:
            max_seq = max((p.get('序号', 0) for p in projects), default=0)
            new_project['序号'] = max_seq + 1
        projects.append(new_project)
        if save_project_data(projects):
            return jsonify({"message": "项目创建成功"}), 201
        return jsonify({"error": "保存数据失败"}), 500
    except Exception as e:
        return jsonify({"error": str(e)}), 500


@bp.route('/api/projects/<int:project_id>', methods=['PATCH'])
def update_project(project_id):
    try:
        projects = load_project_data()
        updates = request.json
        target_project = None
        for project in projects:
            if project['序号'] == project_id:
                target_project = project
                if updates.get('导入状态') == '已导入' and project.get('导入状态') == '已导入':
                    creation_time = project.get('创建时间', '')
                    for p in projects:
                        if p.get('创建时间') == creation_time:
                            p['提取状态'] = '未提取'
                            p['校审状态'] = '未校审'
                            p['校审时间'] = ''
                allowed_fields = ['导入状态', '提取状态', '校审状态', '校审时间']
                for field in allowed_fields:
                    if field in updates:
                        project[field] = updates[field]
                break
        if target_project is None:
            return jsonify({"error": "项目不存在"}), 404
        if save_project_data(projects):
            return jsonify({"message": "项目更新成功"})
        return jsonify({"error": "保存数据失败"}), 500
    except Exception as e:
        return jsonify({"error": str(e)}), 500


@bp.route('/api/projects/<int:project_id>/extract', methods=['POST'])
def extract_indicators(project_id):
    try:
        benti_data = load_json_data(BENTI_DATA_FILE)
        qita_data = load_json_data(QITA_DATA_FILE)
        if not benti_data or not qita_data:
            return jsonify({"error": "无法加载指标数据"}), 500
        projects = load_project_data()
        for project in projects:
            if project['序号'] == project_id:
                project['提取状态'] = '已提取'
                break
        if save_project_data(projects):
            return jsonify({"message": "指标提取成功", "status": "success"})
        return jsonify({"error": "保存数据失败"}), 500
    except Exception as e:
        return jsonify({"error": str(e)}), 500


@bp.route('/api/projects/<int:project_id>/review', methods=['POST'])
def review_indicators(project_id):
    try:
        benti_data = load_json_data(BENTI_DATA_FILE)
        qita_data = load_json_data(QITA_DATA_FILE)
        if not benti_data or not qita_data:
            return jsonify({"error": "无法加载指标数据"}), 500
        projects = load_project_data()
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        for project in projects:
            if project['序号'] == project_id:
                project['校审状态'] = '已校审'
                project['校审时间'] = current_time
                break
        if save_project_data(projects):
            review_data = {'benti': {}, 'qita': {}}
            # 生成校审结果（逻辑与原 app.py 保持一致）
            for indicator_source, ind_data in [('benti', benti_data), ('qita', qita_data)]:
                if ind_data:
                    row = ind_data[0]
                    for key, value in row.items():
                        if isinstance(value, (int, float)) and key not in ['序号']:
                            is_abnormal = value > 80
                            min_value, max_value = 25, 45
                            review_data[indicator_source][key] = {
                                'value': value,
                                'status': '异常' if is_abnormal else '正常',
                                'detail': (
                                    f'指标值偏离历史均值较大：历史指标值区间为【{min_value}-{max_value}】，'
                                    f'现指标值为{value}，异常。' if is_abnormal else
                                    f'指标值在合理范围内：历史指标值区间为【{min_value}-{max_value}】，'
                                    f'现指标值为{value}，正常。'
                                )
                            }
            return jsonify({"message": "指标校审成功", "status": "success", "review_data": review_data})
        return jsonify({"error": "保存数据失败"}), 500
    except Exception as e:
        return jsonify({"error": str(e)}), 500 


@bp.route('/api/review/export/<int:project_id>')
def export_review_word(project_id):
    # 加载指标信息
    ind_info_path = os.path.join(DATA_DIR, 'Ind_info.json')
    with open(ind_info_path, 'r', encoding='utf-8') as f:
        ind_info = json.load(f)
    benti_info = ind_info.get('本体费用指标', {})
    qita_info = ind_info.get('其他费用指标', {})

    # 随机演示数据
    project_info = {
        '工程名称': '500kV 东莞西南部受电通道工程',
        '线路段名称': '500kV 博罗 - 莞城双回线路 - 惠州段（25m 风区）',
        '电压等级': '500kV',
        '线路总长度(km)': '22.3',
        '回路数': '双回路',
        '风速(m/s)': '25',
        '覆冰(mm)': '10',
        '导线规格': 'JL/LB20A 630/45',
    }
    # 校审摘要
    summary_data = [
        ['本体费用指标', '校审成功', '指标数据正常', 40, '80%'],
        ['本体费用指标', '校审成功', '指标数据异常', 2, '4%'],
        ['本体费用指标', '校审失败', '/', 2, '4%'],
        ['其他费用指标', '校审成功', '指标数据正常', 23, '10%'],
        ['其他费用指标', '校审成功', '指标数据异常', 2, '2%'],
        ['其他费用指标', '校审失败', '/', 1, '2%'],
    ]
    # 校审明细（指标名称和单位从 ind_info 获取）
    benti_detail = []
    for i, key in enumerate(benti_info.keys()):
        info = benti_info[key]
        name = info.get('指标名称') if isinstance(info, dict) else info.get('名称')
        unit = info.get('指标单位') if isinstance(info, dict) else info.get('单位')
        benti_detail.append([
            i + 1,
            name,
            unit,
            round(random.uniform(20, 60), 2),
            random.choice(['正常', '异常']),
            '明细说明'
        ])
    qita_detail = []
    for i, key in enumerate(qita_info.keys()):
        info = qita_info[key]
        name = info.get('指标名称') if isinstance(info, dict) else info.get('名称')
        unit = info.get('指标单位') if isinstance(info, dict) else info.get('单位')
        qita_detail.append([
            i + 1,
            name,
            unit,
            round(random.uniform(1, 10), 2),
            random.choice(['正常', '异常']),
            '明细说明'
        ])
    # 校审建议
    suggestions = [
        '核实 [指标项 1] 工程量及费用指标',
        '核实 [指标项 2] 工程量及费用指标',
        '[其他校审建议]'
    ]

    doc = Document()
    # 设置全局字体为宋体、黑色
    style = doc.styles['Normal']
    style.font.name = u'宋体'
    style._element.rPr.rFonts.set(qn('w:eastAsia'), u'宋体')
    style.font.size = Pt(10.5)
    style.font.color.rgb = RGBColor(0, 0, 0)  # 明确设置为黑色

    def set_heading_style(paragraph, level=1):
        """
        设置标题样式：
        - 0级标题（主标题）：黑色、宋体、居中
        - 其他级别标题：黑色、宋体、左对齐
        """
        if level == 0:
            paragraph.alignment = 1  # 居中
        else:
            paragraph.alignment = 0  # 左对齐
        for run in paragraph.runs:
            run.font.color.rgb = RGBColor(0, 0, 0)
            run.font.name = u'宋体'
            r = run._element
            r.rPr.rFonts.set(qn('w:eastAsia'), u'宋体')

    # 添加主标题并设置样式（居中、黑色、宋体）
    heading = doc.add_heading('技经数据校审自校单', 0)
    set_heading_style(heading, level=0)

    # 去除主标题下的蓝色横线
    if heading._element.xpath('.//w:pBdr'):
        for bdr in heading._element.xpath('.//w:pBdr'):
            bdr.getparent().remove(bdr)

    doc.add_paragraph('')
    heading1 = doc.add_heading('1. 线路工程基本信息', level=1)
    set_heading_style(heading1, level=1)

    table = doc.add_table(rows=8, cols=2)
    table.style = 'Table Grid'
    info_keys = list(project_info.keys())
    for i, key in enumerate(info_keys):
        table.cell(i, 0).text = key
        table.cell(i, 1).text = str(project_info[key])

    doc.add_paragraph('')
    heading2 = doc.add_heading('2. 参考历史线路工程', level=1)
    set_heading_style(heading2, level=1)
    doc.add_paragraph('参考历史线路工程：选用的乌东德配套交流 27m/s 风、10mm 冰或者某几个特征段的组合')
    doc.add_paragraph('')
    heading3 = doc.add_heading('3. 数据校审信息', level=1)
    set_heading_style(heading3, level=1)
    heading31 = doc.add_heading('3.1. 指标校审摘要', level=2)
    set_heading_style(heading31, level=2)
    summary_table = doc.add_table(rows=1, cols=5)
    summary_table.style = 'Table Grid'
    hdr_cells = summary_table.rows[0].cells
    hdr_cells[0].text = '指标类别'
    hdr_cells[1].text = '校审状态'
    hdr_cells[2].text = '指标数据状态'
    hdr_cells[3].text = '个数'
    hdr_cells[4].text = '占比'
    for row in summary_data:
        cells = summary_table.add_row().cells
        for j, val in enumerate(row):
            cells[j].text = str(val)
    doc.add_paragraph('')
    heading32 = doc.add_heading('3.2. 指标校审详情', level=2)
    set_heading_style(heading32, level=2)
    heading321 = doc.add_heading('3.2.1. 本体费用指标', level=3)
    set_heading_style(heading321, level=3)
    benti_table = doc.add_table(rows=1, cols=6)
    benti_table.style = 'Table Grid'
    benti_hdr = benti_table.rows[0].cells
    benti_hdr[0].text = '序号'
    benti_hdr[1].text = '指标名称'
    benti_hdr[2].text = '单位'
    benti_hdr[3].text = '指标值'
    benti_hdr[4].text = '校审结果'
    benti_hdr[5].text = '校审明细'
    for row in benti_detail:
        cells = benti_table.add_row().cells
        for j, val in enumerate(row):
            cells[j].text = str(val)
    doc.add_paragraph('')
    heading322 = doc.add_heading('3.2.2. 其他费用指标', level=3)
    set_heading_style(heading322, level=3)
    qita_table = doc.add_table(rows=1, cols=6)
    qita_table.style = 'Table Grid'
    qita_hdr = qita_table.rows[0].cells
    qita_hdr[0].text = '序号'
    qita_hdr[1].text = '指标名称'
    qita_hdr[2].text = '单位'
    qita_hdr[3].text = '指标值'
    qita_hdr[4].text = '校审结果'
    qita_hdr[5].text = '校审明细'
    for row in qita_detail:
        cells = qita_table.add_row().cells
        for j, val in enumerate(row):
            cells[j].text = str(val)
    doc.add_paragraph('')
    heading4 = doc.add_heading('4. 校审建议', level=1)
    set_heading_style(heading4, level=1)
    for sug in suggestions:
        doc.add_paragraph(sug, style='List Number')
    # 输出到内存流
    file_stream = io.BytesIO()
    doc.save(file_stream)
    file_stream.seek(0)
    filename = f"xxxx线路工程指标自校单_{project_id}.docx"
    return send_file(file_stream, as_attachment=True, download_name=filename, mimetype='application/vnd.openxmlformats-officedocument.wordprocessingml.document')


@bp.route('/ruleAudit.html')
def rule_audit():
    return render_template('rule/ruleAudit.html')