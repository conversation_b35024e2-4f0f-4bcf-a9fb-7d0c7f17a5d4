<!-- 工程列表 -->
<section class="project-section">
    <!-- 导航栏 -->
    <div class="project-panel-nav">
        <div class="project-nav-left">
            <h2 class="project-nav-title">工程管理</h2>
            <div class="project-nav-breadcrumb">
                <span>快速组价</span>
                <i class="project-nav-separator">/</i>
                <span class="current">工程管理</span>
            </div>
        </div>
        <div class="project-nav-right">
            <span class="project-nav-stats">共 <span id="projectCount">0</span> 个工程</span>
            <button type="button" class="btn btn-primary" onclick="showCreateProjectModal()">新建工程</button>
        </div>
    </div>

    <!-- 查询条件 -->
    <form class="query-form" id="searchForm">
        <div class="search-conditions">
            <div class="form-row">
                <div class="form-group">
                    <label>工程名称</label>
                    <input type="text" id="searchProjectName" placeholder="请输入工程名称">
                </div>
                <div class="form-group voltage-group">
                    <label>电压等级</label>
                    <select id="searchVoltageLevel">
                        <option value="">全部</option>
                        <option value="500kV">500kV</option>
                        <option value="220kV">220kV</option>
                        <option value="110kV">110kV</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>线路总长度(km)</label>
                    <input type="number" id="searchLineLength" placeholder="请输入线路长度">
                </div>
                <div class="form-group button-group-inline">
                    <label>&nbsp;</label>
                    <div class="button-container">
                        <button type="button" class="btn btn-primary" onclick="searchProjects()">查询</button>
                        <button type="reset" class="btn btn-secondary">重置</button>
                    </div>
                </div>
            </div>
        </div>
    </form>

    <div class="content-wrapper">
        <div class="table-container">
            <table class="project-table">
                <thead>
                    <tr>
                        <th>工程名称</th>
                        <th>电压等级</th>
                        <th>线路总长度</th>
                        <th>特征段数量</th>
                        <!-- <th>指标汇总状态</th> -->
                        <th>组价状态</th>
                        <th>创建时间</th>
                        <th>组价时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="projectTableBody">
                    <!-- 项目数据将通过JavaScript动态填充 -->
                    <template id="projectRowTemplate">
                        <tr>
                            <td data-field="工程名称"></td>
                            <td data-field="电压等级"></td>
                            <td data-field="线路总长度"></td>
                            <td data-field="特征段数量"></td>
                            <!-- <td><span class="status-tag" data-field="指标汇总状态"></span></td> -->
                            <td><span class="status-tag" data-field="组价状态"></span></td>
                            <td data-field="创建时间"></td>
                            <td data-field="组价时间"></td>
                            <td>
                                <button class="btn btn-primary" onclick="selectProject(this)" data-project-id="">特征段管理</button>
                                <button class="btn btn-primary pricing-sum-btn" onclick="pricingSum(this)" data-project-id="">组价汇总</button>
                                <!-- <button class="btn btn-primary view-pricing-btn" onclick="showProjectPricingResult(this)" data-project-id="" style="display: none;">查看组价</button> -->
                                <button class="btn btn-danger" onclick="deleteProject(this)" data-project-id="">删除</button>
                            </td>
                        </tr>
                    </template>
                </tbody>
            </table>
        </div>

        <div class="pagination-wrapper">
            <div class="pagination">
                <button class="active">1</button>
                <button>2</button>
                <button>3</button>
                <button>4</button>
                <button>5</button>
            </div>
        </div>
    </div>
</section>

<!-- 新建工程对话框 -->
<div id="createProjectModal" class="indicator-modal">
    <div class="indicator-content">
        <span class="close" onclick="closeCreateProjectModal()">&times;</span>
        <h2>新建工程</h2>
        <form id="createProjectForm" class="add-project-form">
            <div class="form-row">
                <div class="form-group">
                    <label>工程名称</label>
                    <input type="text" id="projectName" placeholder="请输入工程名称" required>
                </div>
                <!-- <div class="form-group">
                    <label>电压等级1</label>
                    <select id="voltageLevel">
                        <option value="500kV">500kV</option>
                        <option value="220kV">220kV</option>
                        <option value="110kV">110kV</option>
                    </select>
                </div> -->
                <div class="form-group">
                    <label>电压等级</label>
                    <select id="voltageLevelNew">
                        <option value="500kV">500kV</option>
                        <option value="220kV">220kV</option>
                        <option value="110kV">110kV</option>
                    </select>
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label>线路总长度(km)</label>
                    <input type="number" id="lineLength" step="0.1" placeholder="请输入线路长度" required>
                </div>
                <div class="form-group">
                    <label>备注</label>
                    <input type="text" id="projectRemark" placeholder="请输入备注信息">
                </div>
            </div>
            <div class="form-actions">
                <button type="button" class="btn btn-primary" onclick="saveNewProject()">保存</button>
                <button type="button" class="btn btn-secondary" onclick="closeCreateProjectModal()">取消</button>
            </div>
        </form>
    </div>
</div>

<!-- 组价结果查看对话框 -->
<div id="projectPricingResultModal" class="indicator-modal">
    <div class="indicator-content pricing-result-content">
        <span class="close" onclick="closeProjectPricingResultModal()">&times;</span>
        <div class="modal-header">
            <h2>组价结果</h2>
            <button class="btn btn-primary export-btn" onclick="exportProjectPricingResult()">
                <i class="fas fa-file-export"></i> 导出Excel
            </button>
        </div>
        
        <!-- 工程信息展示区域 -->
        <div class="project-info-panel">
            <div class="project-info-item">
                <label>工程名称</label>
                <span id="resultProjectName">500kV东莞西南部受电通道工程</span>
            </div>
            <div class="project-info-item">
                <label>线路总长度(km)</label>
                <span id="resultLineLength">25</span>
            </div>
            <div class="project-info-item">
                <label>电压等级</label>
                <span id="resultVoltageLevel">500kV</span>
            </div>
            <div class="project-info-item">
                <label>特征段数量</label>
                <span id="resultSectionCount">3</span>
            </div>
        </div>
        
        <div class="preview-tabs">
            <button class="preview-tab active" onclick="switchProjectPricingTab('benti')">本体费用指标</button>
            <button class="preview-tab" onclick="switchProjectPricingTab('qita')">其他费用指标</button>
        </div>
        
        <!-- 本体费用面板 -->
        <div id="bentiPanel" class="preview-panel active">
            <div class="result-table-container">
                <table class="pricing-table">
                    <thead>
                        <tr>
                            <th>指标项</th>
                            <th>单位</th>
                            <th>指标量（总）</th>
                            <th>单价（元）</th>
                            <th>总价（万元）</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>铁塔基数</td>
                            <td>基</td>
                            <td>50</td>
                            <td>25000</td>
                            <td>125</td>
                        </tr>
                        <tr>
                            <td>直线塔</td>
                            <td>基</td>
                            <td>40</td>
                            <td>20000</td>
                            <td>80</td>
                        </tr>
                        <tr>
                            <td>耐张塔</td>
                            <td>基</td>
                            <td>10</td>
                            <td>45000</td>
                            <td>45</td>
                        </tr>
                        <tr>
                            <td>导线</td>
                            <td>t</td>
                            <td>30</td>
                            <td>35000</td>
                            <td>105</td>
                        </tr>
                        <tr>
                            <td>塔材</td>
                            <td>t</td>
                            <td>200</td>
                            <td>15000</td>
                            <td>300</td>
                        </tr>
                        <tr>
                            <td>基础钢材</td>
                            <td>t</td>
                            <td>15</td>
                            <td>12000</td>
                            <td>18</td>
                        </tr>
                        <tr>
                            <td>地脚螺栓和插入式角钢</td>
                            <td>t</td>
                            <td>5</td>
                            <td>15000</td>
                            <td>7.5</td>
                        </tr>
                        <tr>
                            <td>挂线金具</td>
                            <td>t</td>
                            <td>8</td>
                            <td>25000</td>
                            <td>20</td>
                        </tr>
                        <tr>
                            <td>导线间隔棒</td>
                            <td>套</td>
                            <td>100</td>
                            <td>500</td>
                            <td>5</td>
                        </tr>
                        <tr>
                            <td>防振锤</td>
                            <td>个</td>
                            <td>200</td>
                            <td>300</td>
                            <td>6</td>
                        </tr>
                        <tr>
                            <td>导线防振锤</td>
                            <td>个</td>
                            <td>150</td>
                            <td>300</td>
                            <td>4.5</td>
                        </tr>
                        <tr>
                            <td>地线防振锤</td>
                            <td>个</td>
                            <td>50</td>
                            <td>300</td>
                            <td>1.5</td>
                        </tr>
                        <tr>
                            <td>合成复合绝缘子</td>
                            <td>支</td>
                            <td>300</td>
                            <td>1000</td>
                            <td>30</td>
                        </tr>
                        <tr>
                            <td>玻璃绝缘子/盘式绝缘子</td>
                            <td>支</td>
                            <td>0</td>
                            <td>0</td>
                            <td>0</td>
                        </tr>
                        <tr>
                            <td>硬跳</td>
                            <td>套</td>
                            <td>50</td>
                            <td>2000</td>
                            <td>10</td>
                        </tr>
                        <tr>
                            <td>现浇混凝土</td>
                            <td>m³</td>
                            <td>1000</td>
                            <td>800</td>
                            <td>80</td>
                        </tr>
                        <tr>
                            <td>灌柱桩基础混凝土</td>
                            <td>m³</td>
                            <td>500</td>
                            <td>1000</td>
                            <td>50</td>
                        </tr>
                        <tr>
                            <td>基础护壁</td>
                            <td>m³</td>
                            <td>100</td>
                            <td>600</td>
                            <td>6</td>
                        </tr>
                        <tr>
                            <td>基础垫层</td>
                            <td>m³</td>
                            <td>50</td>
                            <td>500</td>
                            <td>2.5</td>
                        </tr>
                        <tr>
                            <td>钻孔灌注桩深度</td>
                            <td>m</td>
                            <td>800</td>
                            <td>1000</td>
                            <td>80</td>
                        </tr>
                        <tr>
                            <td>护坡、挡土墙</td>
                            <td>m³</td>
                            <td>120</td>
                            <td>1000</td>
                            <td>12</td>
                        </tr>
                        <tr>
                            <td>土方量</td>
                            <td>m³</td>
                            <td>5000</td>
                            <td>100</td>
                            <td>50</td>
                        </tr>
                        <tr>
                            <td>基坑土方（非机械）</td>
                            <td>m³</td>
                            <td>1500</td>
                            <td>200</td>
                            <td>30</td>
                        </tr>
                        <tr>
                            <td>基坑土方（机械）</td>
                            <td>m³</td>
                            <td>3500</td>
                            <td>150</td>
                            <td>52.5</td>
                        </tr>
                        <tr>
                            <td>接地槽</td>
                            <td>m³</td>
                            <td>150</td>
                            <td>400</td>
                            <td>6</td>
                        </tr>
                        <tr>
                            <td>排水沟</td>
                            <td>m³</td>
                            <td>100</td>
                            <td>500</td>
                            <td>5</td>
                        </tr>
                        <tr>
                            <td>尖峰、基面</td>
                            <td>m³</td>
                            <td>50</td>
                            <td>600</td>
                            <td>3</td>
                        </tr>
                        <tr>
                            <td>本体工程</td>
                            <td>万元</td>
                            <td>8000</td>
                            <td>-</td>
                            <td>8000</td>
                        </tr>
                        <tr>
                            <td>基础工程</td>
                            <td>万元</td>
                            <td>1500</td>
                            <td>-</td>
                            <td>1500</td>
                        </tr>
                        <tr>
                            <td>杆塔工程</td>
                            <td>万元</td>
                            <td>3200</td>
                            <td>-</td>
                            <td>3200</td>
                        </tr>
                        <tr>
                            <td>接地工程</td>
                            <td>万元</td>
                            <td>400</td>
                            <td>-</td>
                            <td>400</td>
                        </tr>
                        <tr>
                            <td>架线工程</td>
                            <td>万元</td>
                            <td>1600</td>
                            <td>-</td>
                            <td>1600</td>
                        </tr>
                        <tr>
                            <td>附件工程</td>
                            <td>万元</td>
                            <td>240</td>
                            <td>-</td>
                            <td>240</td>
                        </tr>
                        <tr>
                            <td>辅助工程</td>
                            <td>万元</td>
                            <td>160</td>
                            <td>-</td>
                            <td>160</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        
        <!-- 其他费用面板 -->
        <div id="qitaPanel" class="preview-panel">
            <div class="result-table-container">
                <table class="pricing-table">
                    <thead>
                        <tr>
                            <th>指标项</th>
                            <th>单位</th>
                            <th>指标量（总）</th>
                            <th>单价（元）</th>
                            <th>总价（万元）</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>项目建设管理费</td>
                            <td>万元</td>
                            <td>4000</td>
                            <td>-</td>
                            <td>4000</td>
                        </tr>
                        <tr>
                            <td>项目法人管理费</td>
                            <td>万元</td>
                            <td>2000</td>
                            <td>-</td>
                            <td>2000</td>
                        </tr>
                        <tr>
                            <td>招标费</td>
                            <td>万元</td>
                            <td>800</td>
                            <td>-</td>
                            <td>800</td>
                        </tr>
                        <tr>
                            <td>工程监理费</td>
                            <td>万元</td>
                            <td>1600</td>
                            <td>-</td>
                            <td>1600</td>
                        </tr>
                        <tr>
                            <td>施工过程造价咨询及竣工结算审核费</td>
                            <td>万元</td>
                            <td>400</td>
                            <td>-</td>
                            <td>400</td>
                        </tr>
                        <tr>
                            <td>工程保险费</td>
                            <td>万元</td>
                            <td>1200</td>
                            <td>-</td>
                            <td>1200</td>
                        </tr>
                        <tr>
                            <td>项目建设技术服务费</td>
                            <td>万元</td>
                            <td>240</td>
                            <td>-</td>
                            <td>240</td>
                        </tr>
                        <tr>
                            <td>项目前期工作费</td>
                            <td>万元</td>
                            <td>320</td>
                            <td>-</td>
                            <td>320</td>
                        </tr>
                        <tr>
                            <td>勘察设计费</td>
                            <td>万元</td>
                            <td>480</td>
                            <td>-</td>
                            <td>480</td>
                        </tr>
                        <tr>
                            <td>勘察费</td>
                            <td>万元</td>
                            <td>160</td>
                            <td>-</td>
                            <td>160</td>
                        </tr>
                        <tr>
                            <td>设计费</td>
                            <td>万元</td>
                            <td>320</td>
                            <td>-</td>
                            <td>320</td>
                        </tr>
                        <tr>
                            <td>基本设计费</td>
                            <td>万元</td>
                            <td>2400</td>
                            <td>-</td>
                            <td>2400</td>
                        </tr>
                        <tr>
                            <td>其他设计费</td>
                            <td>万元</td>
                            <td>800</td>
                            <td>-</td>
                            <td>800</td>
                        </tr>
                        <tr>
                            <td>设计文件评审费</td>
                            <td>万元</td>
                            <td>40</td>
                            <td>-</td>
                            <td>40</td>
                        </tr>
                        <tr>
                            <td>可行性研究文件评审费</td>
                            <td>万元</td>
                            <td>16</td>
                            <td>-</td>
                            <td>16</td>
                        </tr>
                        <tr>
                            <td>初步设计文件评审费</td>
                            <td>万元</td>
                            <td>12</td>
                            <td>-</td>
                            <td>12</td>
                        </tr>
                        <tr>
                            <td>施工图文件评审费</td>
                            <td>万元</td>
                            <td>12</td>
                            <td>-</td>
                            <td>12</td>
                        </tr>
                        <tr>
                            <td>工程建设检测费</td>
                            <td>万元</td>
                            <td>80</td>
                            <td>-</td>
                            <td>80</td>
                        </tr>
                        <tr>
                            <td>电力工程质量检测费</td>
                            <td>万元</td>
                            <td>60</td>
                            <td>-</td>
                            <td>60</td>
                        </tr>
                        <tr>
                            <td>桩基检测费</td>
                            <td>万元</td>
                            <td>20</td>
                            <td>-</td>
                            <td>20</td>
                        </tr>
                        <tr>
                            <td>电力工程技术经济标准编制费</td>
                            <td>万元</td>
                            <td>40</td>
                            <td>-</td>
                            <td>40</td>
                        </tr>
                        <tr>
                            <td>生产准备费</td>
                            <td>万元</td>
                            <td>160</td>
                            <td>-</td>
                            <td>160</td>
                        </tr>
                        <tr>
                            <td>管理车辆购置费</td>
                            <td>万元</td>
                            <td>10</td>
                            <td>-</td>
                            <td>10</td>
                        </tr>
                        <tr>
                            <td>工器具及办公家具购置费</td>
                            <td>万元</td>
                            <td>20</td>
                            <td>-</td>
                            <td>20</td>
                        </tr>
                        <tr>
                            <td>生产职工培训及提前进场费</td>
                            <td>万元</td>
                            <td>32</td>
                            <td>-</td>
                            <td>32</td>
                        </tr>
                        <tr>
                            <td>专业爆破服务费</td>
                            <td>万元</td>
                            <td>80</td>
                            <td>-</td>
                            <td>80</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<style>
/* 工程列表区域样式 */
.project-section {
    padding: 0.75rem;
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden; /* 防止整个section出现滚动条 */
}

/* 导航栏样式 */
.project-panel-nav {
    background: white;
    padding: 1rem 1.5rem;
    margin-bottom: 1rem;
    border-radius: 4px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-shrink: 0;
}

.project-nav-left {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.project-nav-title {
    font-size: 1.25rem;
    font-weight: 500;
    color: var(--text-color);
    margin: 0;
}

.project-nav-breadcrumb {
    color: rgba(0, 0, 0, 0.45);
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.project-nav-separator {
    color: rgba(0, 0, 0, 0.25);
    font-style: normal;
}

.project-nav-breadcrumb .current {
    color: var(--text-color);
}

.project-nav-right {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.project-nav-stats {
    font-size: 0.875rem;
    color: rgba(0, 0, 0, 0.65);
}

/* 查询表单样式 */
.query-form {
    background: white;
    padding: 0.75rem;
    margin-bottom: 1rem;
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.search-conditions {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.form-row {
    display: flex;
    align-items: flex-end;
    gap: 0.75rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    min-width: 180px;
}

.form-group label {
    margin-bottom: 0.25rem;
    color: var(--text-color);
    font-size: 0.875rem;
}

.form-group input,
.form-group select {
    padding: 0.25rem 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    height: 28px;
}

.button-group {
    display: flex;
    gap: 0.5rem;
    padding-top: 0.5rem;
    border-top: 1px solid var(--border-color);
}

/* 内联按钮组样式 */
.button-group-inline {
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
}

/* --- 查 询 区按钮对齐优化 --- */
.button-container {
    height: auto;            /* 取消固定高度 */
    align-items: center;     /* 垂直居中 */
}

/* 查询表单内按钮尺寸参考 audit 模块 */
.query-form .btn {
    padding: 12px 24px;
    font-size: 0.9rem;
    border-radius: 980px;
    line-height: 1;
}

.btn {
    padding: 12px 24px;
    border: 1px solid transparent;
    border-radius: 980px;
    font-size: 0.875rem;
    cursor: pointer;
    line-height: 1;
    white-space: nowrap;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-secondary {
    background-color: #f0f0f0;
    border-color: #d9d9d9;
    color: rgba(0, 0, 0, 0.85);
}

/* 内容包装器 */
.content-wrapper {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0; /* 确保flex子元素可以正确滚动 */
    background: white;
    overflow: hidden; /* 防止content-wrapper出现滚动条 */
}

/* 表格容器样式 */
.table-container {
    flex: 1;
    overflow: auto;
    margin-bottom: 0;
}

.project-table {
    width: 100%;
    border-collapse: collapse;
}

.project-table th,
.project-table td {
    padding: 0.5rem;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
    font-size: 0.875rem;
    white-space: nowrap;
}

.project-table th {
    background-color: #fafafa;
    font-weight: 500;
    color: rgba(0, 0, 0, 0.85);
}

.project-table tbody tr:hover {
    background-color: #fafafa;
}

/* 状态标签样式 */
.status-tag {
    padding: 0.125rem 0.5rem;
    border-radius: 2px;
    font-size: 0.75rem;
    display: inline-block;
}

.status-default {
    background-color: #f5f5f5;
    color: rgba(0, 0, 0, 0.65);
}

.status-success {
    background-color: #f6ffed;
    color: #52c41a;
}

.status-warning {
    background-color: #fff7e6;
    color: #faad14;
}

/* 分页包装器 */
.pagination-wrapper {
    background: white;
    padding: 0.5rem;
    border-top: 1px solid var(--border-color);
    margin-top: auto;
    flex-shrink: 0; /* 防止分页区域被压缩 */
}

.pagination {
    display: flex;
    justify-content: center;
    gap: 0.25rem;
    margin-top: 0.5rem;
}

.pagination button {
    padding: 0.25rem 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: 2px;
    background: white;
    cursor: pointer;
    font-size: 0.875rem;
    min-width: 28px;
}


/* 创建工程对话框样式 */
.indicator-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
}

.indicator-modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

.indicator-content {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    width: 100%;
    max-width: 600px;
    position: relative;
}

.close {
    position: absolute;
    right: 1rem;
    top: 1rem;
    font-size: 1.25rem;
    cursor: pointer;
    color: rgba(0, 0, 0, 0.45);
}

/* Toast提示样式 */
.toast {
    position: fixed;
    top: 16px;
    right: 16px;
    padding: 0.75rem 1rem;
    background: white;
    border-radius: 2px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    animation: slideIn 0.3s ease;
    font-size: 0.875rem;
}

.toast.success {
    background: #f6ffed;
    border: 1px solid #b7eb8f;
    color: #52c41a;
}

.toast.error {
    background: #fff2f0;
    border: 1px solid #ffccc7;
    color: #ff4d4f;
}

.toast.info {
    background: #e6f7ff;
    border: 1px solid #91d5ff;
    color: #1890ff;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* 选中行样式 */
.project-table tr.selected {
    background-color: #e6f7ff;
}

/* 电压等级下拉框特殊样式 */
.voltage-group select {
    font-size: 12px !important;
    width: 100%;
}

.voltage-group select option {
    font-size: 12px !important;
}

/* 组价结果对话框样式 */
.pricing-result-content {
    width: 90%;
    max-width: 90% !important;
    height: 90vh;
    display: flex;
    flex-direction: column;
    margin: 5vh auto; /* 居中 */
    overflow-y: auto; /* 可滚动 */
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.modal-header h2 {
    margin: 0;
}

.export-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.export-btn i {
    font-size: 0.875rem;
}

.preview-tabs {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 0.5rem;
}

.preview-tab {
    padding: 0.5rem 1rem;
    border: none;
    background: none;
    cursor: pointer;
    font-size: 0.875rem;
    color: rgba(0, 0, 0, 0.65);
    position: relative;
}

.preview-tab.active {
    color: var(--primary-color);
    font-weight: 500;
}

.preview-tab.active::after {
    content: '';
    position: absolute;
    bottom: -0.5rem;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: var(--primary-color);
}

.preview-panel {
    display: none;
    flex: 1;
    overflow: hidden; /* 防止面板本身出现滚动条 */
}

.preview-panel.active {
    display: block;
}

.pricing-table {
    width: 100%;
    border-collapse: collapse;
}

.pricing-table th,
.pricing-table td {
    padding: 0.75rem;
    text-align: left;
    border: 1px solid var(--border-color);
    font-size: 0.875rem;
}

.pricing-table th {
    background-color: #fafafa;
    font-weight: 500;
    position: sticky;
    top: 0;
    z-index: 10;
    box-shadow: 0 1px 1px rgba(0,0,0,0.1);
}

.pricing-table td {
    background-color: white;
}

.pricing-table tbody tr:hover {
    background-color: #f5f5f5;
}

/* 状态标签样式 */
.status-tag {
    background-color: #f5f5f5;
    border: 1px solid #d9d9d9;
    color: rgba(0, 0, 0, 0.65);
}

.status-tag.status-success {
    background-color: #f6ffed;
    border: 1px solid #b7eb8f;
    color: #52c41a;
}

/* 新的工程信息面板样式 */
.project-info-panel {
    background: #f5f5f5;
    padding: 1rem;
    border-radius: 4px;
    margin-bottom: 1rem;
    display: flex;
    flex-wrap: wrap;
    gap: 1.5rem;
}

.project-info-panel .project-info-item {
    display: flex;
    flex-direction: column;
    min-width: 160px;
}

.project-info-panel .project-info-item label {
    font-size: 0.75rem;
    color: rgba(0, 0, 0, 0.65);
    margin-bottom: 0.25rem;
}

.project-info-panel .project-info-item span {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-color);
}

.result-table-container {
    max-height: 400px;
    overflow-y: auto; /* 添加垂直滚动条 */
    -webkit-overflow-scrolling: touch; /* 优化移动端滚动 */
    border: 1px solid #eee;
    margin-bottom: 1rem;
}

.project-result-btn {
    /* 额外样式可根据需求调整，这里主要确保一致性 */
    min-width: 70px;
}
</style>

<script src="{{ url_for('static', filename='js/project_management.js') }}"></script>

<script>
// 查看组价结果
function showProjectPricingResult(button) {
    // 直接设置演示数据，不从表格行中获取
    const resultProjectName = document.getElementById('resultProjectName');
    const resultVoltageLevel = document.getElementById('resultVoltageLevel');
    const resultLineLength = document.getElementById('resultLineLength');
    const resultSectionCount = document.getElementById('resultSectionCount');
    
    // 检查元素是否存在，存在才设置内容
    if (resultProjectName) resultProjectName.textContent = '500kV东莞西南部受电通道工程';
    if (resultVoltageLevel) resultVoltageLevel.textContent = '500kV';
    if (resultLineLength) resultLineLength.textContent = '25';
    if (resultSectionCount) resultSectionCount.textContent = '3';
    
    // 显示模态框
    const modal = document.getElementById('projectPricingResultModal');
    if (modal) {
        modal.classList.add('show');
        
        // 默认显示本体费用标签页
        switchProjectPricingTab('benti');
        
        // 设置表格容器的最大高度
        const resultTableContainers = document.querySelectorAll('.result-table-container');
        resultTableContainers.forEach(container => {
            container.style.maxHeight = '400px';
        });
    } else {
        console.error('找不到组价结果模态框元素');
    }
}

// 关闭组价结果对话框
function closeProjectPricingResultModal() {
    const modal = document.getElementById('projectPricingResultModal');
    if (modal) {
        modal.classList.remove('show');
    }
}

// 切换组价结果tab
function switchProjectPricingTab(tabName) {
    // 切换tab按钮状态
    const tabs = document.querySelectorAll('.preview-tab');
    tabs.forEach(tab => {
        tab.classList.remove('active');
        if (tab.textContent.includes(tabName === 'benti' ? '本体费用' : '其他费用')) {
            tab.classList.add('active');
        }
    });
    
    // 切换面板显示
    const bentiPanel = document.getElementById('bentiPanel');
    const qitaPanel = document.getElementById('qitaPanel');
    
    if (!bentiPanel || !qitaPanel) {
        console.error('找不到费用面板元素');
        return;
    }
    
    if (tabName === 'benti') {
        bentiPanel.classList.add('active');
        qitaPanel.classList.remove('active');
    } else {
        bentiPanel.classList.remove('active');
        qitaPanel.classList.add('active');
    }
}

// 导出组价结果
function exportProjectPricingResult() {
    // 确保showToast函数存在
    if (typeof showToast === 'function') {
        showToast('组价excel已导出', 'success');
    } else {
        console.log('组价excel已导出');
        // 如果showToast不存在，尝试使用原生alert
        alert('组价excel已导出');
    }
}

// 组价汇总函数
function pricingSum(button) {
    // 显示开始组价的提示
    showToast('工程将对所有特征段的组价结果进行汇总', 'info');
    
    // 延迟2秒后显示完成提示并更新状态
    setTimeout(() => {
        const row = button.closest('tr');
        
        // 更新指标汇总状态
        const summaryStatusTag = row.querySelector('[data-field="指标汇总状态"]');
        if (summaryStatusTag) {
            summaryStatusTag.textContent = '已汇总';
            summaryStatusTag.className = 'status-tag status-success';
        }
        
        // 更新组价状态
        const statusTag = row.querySelector('[data-field="组价状态"]');
        statusTag.textContent = '已组价';
        statusTag.classList.add('status-tag', 'status-success');
        
        // 更改"组价汇总"按钮为"组价结果"按钮
        const pricingSumBtn = row.querySelector('.pricing-sum-btn');
        if (pricingSumBtn) {
            pricingSumBtn.textContent = '组价结果';
            pricingSumBtn.onclick = function() {
                showProjectPricingResult(this);
            };
            // 更新按钮样式
            pricingSumBtn.classList.remove('btn-primary');
            pricingSumBtn.classList.add('btn-info', 'btn-sm', 'project-result-btn');
        }
        
        // 隐藏"查看组价"按钮（现在不需要了，因为已经把组价汇总按钮改为组价结果按钮）
        const viewButton = row.querySelector('.view-pricing-btn');
        if (viewButton) {
            viewButton.style.display = 'none';
        }
        
        showToast('工程特征段组价汇总完成', 'success');
    }, 2000);
}

// 初始化时设置状态标签和按钮显示
document.addEventListener('DOMContentLoaded', function() {
    const rows = document.querySelectorAll('#projectTableBody tr');
    rows.forEach(row => {
        const summaryStatus = row.querySelector('[data-field="指标汇总状态"]');
        const pricingStatus = row.querySelector('[data-field="组价状态"]');
        const viewButton = row.querySelector('.view-pricing-btn');
        const pricingSumBtn = row.querySelector('.pricing-sum-btn');
        
        // 检查指标汇总状态
        if (summaryStatus && summaryStatus.textContent === '已汇总') {
            summaryStatus.classList.add('status-tag', 'status-success');
        }
        
        // 检查组价状态
        if (pricingStatus && pricingStatus.textContent === '已组价') {
            pricingStatus.classList.add('status-tag', 'status-success');
            
            // 将"组价汇总"按钮更改为"组价结果"按钮
            if (pricingSumBtn) {
                pricingSumBtn.textContent = '组价结果';
                pricingSumBtn.onclick = function() {
                    showProjectPricingResult(this);
                };
                pricingSumBtn.classList.remove('btn-primary');
                pricingSumBtn.classList.add('btn-info', 'btn-sm', 'project-result-btn');
            }
            
            // 隐藏"查看组价"按钮（因为已经把组价汇总按钮改为组价结果按钮）
            if (viewButton) {
                viewButton.style.display = 'none';
            }
        }
    });
});

// 如果全局showToast函数不存在，定义一个简单的实现
if (typeof window.showToast !== 'function') {
    window.showToast = function(message, type) {
        console.log(`[${type}] ${message}`);
        
        // 创建一个toast元素
        const toast = document.createElement('div');
        toast.className = `toast ${type || 'info'}`;
        toast.textContent = message;
        
        // 添加到body
        document.body.appendChild(toast);
        
        // 3秒后自动移除
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 3000);
    };
}
</script> 