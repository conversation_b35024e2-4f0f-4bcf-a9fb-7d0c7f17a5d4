
/* ================== 快速组价模块样式归档区 START ================== */

/* ================= 变量定义 ================= */
:root {
    --primary-color: #0071e3;
    --primary-hover: #003de3da;
    --secondary-color: #86868b;
    --text-color: #1d1d1f;
    --light-gray: #f5f5f7;
    --border-color: #d2d2d7;
    --success-color: #34c759;
    --warning-color: #ff9500;
    --error-color: #ff3b30;
    --box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
} 

/* ================== 快速组价模块样式归档区 START ================== */

/* ========== 公共样式 .pricing-shared ========== */
/* 按钮样式 */
.pricing-shared-btn {
    padding: 12px 24px;
    border: 1px solid transparent;
    border-radius: 980px;
    font-size: 0.875rem;
    cursor: pointer;
    line-height: 1;
    white-space: nowrap;
    transition: var(--transition);
}

.pricing-shared-btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.pricing-shared-btn-primary:hover {
    background-color: var(--primary-hover) !important; /* 使用hover变量颜色，稍微加深 */
    border-color: var(--primary-hover) !important;
    color: white !important;
    box-shadow: none !important;
    transform: none !important;
}

.pricing-shared-btn-secondary {
    background-color: #f0f0f0;
    border-color: #d9d9d9;
    color: rgba(0, 0, 0, 0.85);
}

.pricing-shared-btn-secondary:hover {
    background-color: #e6e6e6 !important; /* 从#f0f0f0加深到#e6e6e6 */
    border-color: #d9d9d9 !important;
    color: rgba(0, 0, 0, 0.85) !important;
    box-shadow: none !important;
    transform: none !important;
}

/* 绿色按钮样式 */
.pricing-shared-btn-success {
    background-color: #52c41a;
    color: white;
    border-color: #52c41a;
}

.pricing-shared-btn-success:hover {
    background-color: #389e0d !important;
    border-color: #389e0d !important;
    color: white !important;
    box-shadow: none !important;
    transform: none !important;
}

.pricing-shared-btn-delete {
  color: #000000;
  background: #f0f0f0;
  border: 1px solid #d9d9d9;
  border-radius: 980px;
  padding: 12px 24px;
  font-size: 14.4px;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.2s;
}
.pricing-shared-btn-delete:hover {
  background: #e6e6e6;
}

/* Toast提示样式 */
.pricing-shared-toast {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 24px;
    border-radius: 4px;
    z-index: 1000;
    animation: slideIn 0.3s ease-out;
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16);
    max-width: 300px;
    word-wrap: break-word;
}

.pricing-shared-toast.success {
    background-color: #f6ffed;
    border: 1px solid #b7eb8f;
    color: #52c41a;
}

.pricing-shared-toast.error {
    background-color: #fff2f0;
    border: 1px solid #ffccc7;
    color: #ff4d4f;
}

.pricing-shared-toast.info {
    background-color: #e6f7ff;
    border: 1px solid #91d5ff;
    color: #1890ff;
}

@keyframes slideIn {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

/* 模态框样式（通用） */
.pricing-shared-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
}

.pricing-shared-modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

.pricing-shared-modal-content {
    background: #fff;
    border-radius: 8px;
    padding: 1.5rem;
    width: 100%;
    max-width: 500px;
    position: relative;
}

/* 新建工程弹出框特殊样式 */
#createProjectModal .pricing-shared-modal-content {
    max-width: 400px;
}

/* 新建特征段模态框特殊样式 */
#createSectionModal .pricing-shared-modal-content {
    max-width: 450px;
}

.pricing-sm-large-modal {
    margin-top: 80px;
    margin-bottom:10px;
    width: 95% !important;
    height: 90% !important;
    max-width: 2000px !important;
    /* max-height: 95vh !important; */
    overflow: auto !important;
    padding: 1rem !important;
}

.pricing-shared-modal-close {
    position: absolute;
    top: 1.5rem;
    right: 1.5rem;
    z-index: 11;
    font-size: 2rem;
    color: #888;
    background: none;
    border: none;
    cursor: pointer;
}

/* 表单公共样式 */
.pricing-shared-form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 0.5rem;
    margin-top: 1.5rem;
    padding-top: 1rem;
    border-top: 1px solid var(--border-color);
}

/* 新建工程和特征段模态框的表单操作区域 */
.pricing-pm-form-actions,
.pricing-sm-form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 0.75rem;
    margin-top: 0.5rem;
    padding-top: 1rem;
    border-top: 1px solid var(--border-color);
}

.pricing-shared-form-group {
    margin-bottom: 1rem;
}

.pricing-shared-form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.pricing-shared-form-control {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: 4px;
}

/* 标签页公共样式 */
.pricing-shared-tabs {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.pricing-shared-tab-button {
    padding: 0.5rem 1rem;
    border: none;
    background: none;
    cursor: pointer;
    position: relative;
    color: rgba(0, 0, 0, 0.65);
}

.pricing-shared-tab-button.active {
    color: var(--primary-color);
    font-weight: 500;
}

.pricing-shared-tab-button.active::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    right: 0;
    height: 2px;
    background-color: var(--primary-color);
}

.pricing-shared-panel {
    display: none;
}

.pricing-shared-panel.active {
    display: block;
}

/* 加载动画样式 */
.pricing-shared-loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 项目信息面板 */
.pricing-shared-info-panel {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    background-color: #fafafa;
    padding: 1rem;
    margin-bottom: 1rem;
    border-radius: 4px;
}

.pricing-shared-info-item {
    display: flex;
    flex-direction: column;
    margin-right: 1.5rem;
}

.pricing-shared-info-item label {
    font-size: 0.75rem;
    color: rgba(0, 0, 0, 0.45);
    margin-bottom: 0.25rem;
}

.pricing-shared-info-item span {
    font-size: 0.875rem;
    font-weight: 500;
    color: rgba(0, 0, 0, 0.85);
}

/* 匹配度标签 */
.pricing-shared-match-tag {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 4px;
    background-color: #e6f3ff;
    color: #1890ff;
    font-size: 12px;
    font-weight: bold;
    min-width: 60px;
}

/* ================= 工程管理页面 ================= */
.pricing-pm-section {
    padding: 0.75rem;
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
}

.pricing-pm-panel-nav {
    background: #fff;
    padding: 1rem 1.5rem;
    margin-bottom: 1rem;
    border-radius: 4px;
    box-shadow: var(--box-shadow);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-shrink: 0;
}

.pricing-pm-nav-left {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.pricing-pm-nav-title {
    font-size: 1.25rem;
    font-weight: 500;
    color: var(--text-color);
    margin: 0;
}

.pricing-pm-nav-breadcrumb {
    color: rgba(0, 0, 0, 0.45);
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.pricing-pm-nav-separator {
    color: rgba(0, 0, 0, 0.25);
    font-style: normal;
}

.pricing-pm-nav-breadcrumb .current {
    color: var(--text-color);
}

.pricing-pm-nav-right {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.pricing-pm-nav-stats {
    font-size: 0.875rem;
    color: rgba(0, 0, 0, 0.65);
}

/* 查询表单样式 */
.pricing-pm-query-form {
    background: #fff;
    padding: 0.75rem;
    margin-bottom: 1rem;
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.pricing-pm-search-conditions {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.pricing-pm-form-row {
    display: flex;
    align-items: flex-end;
    gap: 0.75rem;
}

.pricing-pm-form-column,
.pricing-sm-form-column {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    width: 100%;
}

.pricing-pm-form-group,
.pricing-sm-form-group {
    display: flex;
    flex-direction: column;
    min-width: 180px;
    width: 100%;
}

.pricing-pm-form-group label,
.pricing-sm-form-label {
    margin-bottom: 0.25rem;
    color: var(--text-color);
    font-size: 0.875rem;
}

.pricing-pm-form-group input,
.pricing-pm-form-group select,
.pricing-shared-input,
.pricing-shared-select,
.pricing-shared-textarea {
    padding: 0.5rem 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    height: 36px;
    font-size: 14px;
}

.pricing-shared-textarea {
    height: auto;
    min-height: 80px;
    resize: vertical;
}

/* 添加工程表单样式 */
.pricing-pm-add-project-form,
.pricing-sm-add-section-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

/* 内容包装器 */
.pricing-pm-content-wrapper {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;
    background: #fff;
    overflow: hidden;
}

/* 表格容器样式 */
.pricing-pm-table-container {
    flex: 1;
    overflow: auto;
    margin-bottom: 0;
}

.pricing-pm-table {
    width: 100%;
    border-collapse: collapse;
}

.pricing-pm-table th,
.pricing-pm-table td {
    padding: 0.5rem;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
    font-size: 0.875rem;
    white-space: nowrap;
}

.pricing-pm-table th {
    background-color: #fafafa;
    font-weight: 500;
    color: rgba(0, 0, 0, 0.85);
}

.pricing-pm-table tbody tr:hover {
    background-color: #fafafa;
}

/* 状态标签样式 */
.pricing-pm-status-tag {
    padding: 0.125rem 0.5rem;
    border-radius: 2px;
    font-size: 0.75rem;
    display: inline-block;
    background-color: #f5f5f5;
    border: 1px solid #d9d9d9;
    color: rgba(0, 0, 0, 0.65);
}
.pricing-pm-status-tag.pricing-pm-status-success {
    background-color: #f6ffed;
    border: 1px solid #b7eb8f;
    color: #52c41a;
}


/* 分页包装器 */
.pricing-pm-pagination-wrapper {
    background: #fff;
    padding: 0.5rem;
    border-top: 1px solid var(--border-color);
    margin-top: auto;
    flex-shrink: 0;
}

.pricing-pm-pagination {
    display: flex;
    justify-content: center;
    gap: 0.25rem;
    margin-top: 0.5rem;
}

.pricing-pm-pagination button {
    padding: 0.25rem 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: 2px;
    background: #fff;
    cursor: pointer;
    font-size: 0.875rem;
    min-width: 28px;
}

.pricing-pm-pagination button.active {
    background: var(--primary-color);
    color: #fff;
    border-color: var(--primary-color);
}

/* ================= 组价结果弹窗样式 ================= */
/* 旧的组价结果样式已移至统一的pricing-pm-result-* 类 */

/* ================= 特征段管理页面 ================= */
.pricing-sm-section {
    padding: 0.75rem;
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
}

/* 导航栏样式 */
.pricing-sm-panel-nav {
    background: white;
    padding: 1rem 1.5rem;
    margin-bottom: 1rem;
    border-radius: 4px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-shrink: 0;
}

.pricing-sm-nav-left {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.pricing-sm-nav-title {
    font-size: 1.25rem;
    font-weight: 500;
    color: var(--text-color);
    margin: 0;
}

.pricing-sm-nav-breadcrumb {
    color: rgba(0, 0, 0, 0.45);
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.pricing-sm-nav-separator {
    color: rgba(0, 0, 0, 0.25);
    font-style: normal;
}

.pricing-sm-nav-breadcrumb .current {
    color: var(--text-color);
}

.pricing-sm-nav-right {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.pricing-sm-nav-stats {
    font-size: 0.875rem;
    color: rgba(0, 0, 0, 0.65);
}

/* 工程信息面板 */
.pricing-sm-project-info {
    display: flex;
    flex-wrap: wrap;
    gap: 2rem;
    align-items: center;
    background: white;
    padding: 1rem 1.5rem;
    margin-bottom: 1rem;
    border-radius: 4px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
    width: 100%;
    z-index: 10;
}

.pricing-sm-info-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    min-width: 150px;
}

.pricing-sm-info-item label {
    font-size: 0.875rem;
    color: rgba(0, 0, 0, 0.65);
    margin: 0;
    display: inline-block;
}

.pricing-sm-info-value {
    font-size: 0.875rem;
    font-weight: 700;
    color: rgba(0, 0, 0, 0.85);
    display: inline-block;
}

/* 查询区域 */
.pricing-sm-search-area {
    background: white;
    padding: 1rem 1.5rem;
    margin-bottom: 1rem;
    border-radius: 4px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
}

.pricing-sm-search-form {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 1rem;
    margin-bottom: 1rem;
}

.pricing-sm-search-item {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.pricing-sm-search-item label {
    font-size: 0.875rem;
    color: rgba(0, 0, 0, 0.85);
}

.pricing-sm-search-item input,
.pricing-sm-search-item select {
    padding: 0.375rem 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 0.875rem;
    width: 100%;
}

.pricing-sm-search-actions {
    display: flex;
    justify-content: flex-end;
    gap: 0.75rem;
    padding-top: 0.5rem;
    border-top: 1px solid var(--border-color);
}

/* 内容包装器 */
.pricing-sm-content-wrapper {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;
    background: white;
    overflow: hidden;
}

/* 表格容器 */
.pricing-sm-table-container {
    flex: 1;
    overflow: auto;
    margin-bottom: 0;
}

.pricing-sm-table {
    width: 100%;
    border-collapse: collapse;
    min-width: 1200px;
}

.pricing-sm-table th,
.pricing-sm-table td {
    padding: 0.75rem 1rem;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
    font-size: 0.875rem;
}

.pricing-sm-table th {
    background-color: #fafafa;
    font-weight: 500;
    color: rgba(0, 0, 0, 0.85);
    white-space: nowrap;
}

.pricing-sm-table tbody tr:hover {
    background-color: #fafafa;
}

/* 状态标签 */
.pricing-sm-status-tag {
    padding: 0.125rem 0.5rem;
    border-radius: 2px;
    font-size: 0.75rem;
    display: inline-block;
}

.pricing-sm-status-default {
    background-color: #f5f5f5;
    color: rgba(0, 0, 0, 0.65);
}

.pricing-sm-status-success {
    background-color: #f6ffed;
    color: #52c41a;
}

.pricing-sm-status-warning {
    background-color: #fff7e6;
    color: #faad14;
}

/* 分页容器 */
.pricing-sm-pagination-wrapper {
    background: white;
    padding: 0.5rem;
    border-top: 1px solid var(--border-color);
    margin-top: auto;
    flex-shrink: 0;
}

.pricing-sm-pagination {
    display: flex;
    justify-content: center;
    gap: 0.25rem;
    margin-top: 0.5rem;
}

.pricing-sm-pagination button {
    padding: 0.25rem 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: 2px;
    background: white;
    cursor: pointer;
    font-size: 0.875rem;
    min-width: 28px;
}

.pricing-sm-pagination button.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* 特征段组价页面 */
.pricing-sp-container {
    padding-top: 10px;
    height: 100%;
    overflow: hidden;
    /* 让头部始终可见，并确保内容区域可滚动 */
    display: flex;
    flex-direction: column;
}

.pricing-sp-section {
    flex: 1 1 auto;
    overflow-y: auto;
}

/* 基本布局 */
.pricing-sp-page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    position: sticky;
    top: 0;
    background: #fff;
    z-index: 2;
}

.pricing-sp-page-title {
    font-size: 1.5rem;
    font-weight: bold;
    margin: 0;
    padding: 0;
    color: rgba(0, 0, 0, 0.85);
    line-height: 1.4;
    visibility: visible;
    display: block !important;
    opacity: 1 !important;
}

.pricing-sp-page-actions {
    display: flex;
    gap: 0.75rem;
}

/* 特征段信息卡片 */
.pricing-sp-feature-info {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    background-color: #f9f9f9;
    padding: 15px;
    border-radius: 5px;
    margin-top: 15px;
}

.pricing-sp-feature-info-item {
    display: flex;
    flex-direction: column;
    min-width: 120px;
}

.pricing-sp-feature-info-item.name {
    width: 300px;
}

.pricing-sp-feature-info-item.length,
.pricing-sp-feature-info-item.wind-speed,
.pricing-sp-feature-info-item.ice-thickness,
.pricing-sp-feature-info-item.circuit-count {
    width: 150px;
}

.pricing-sp-feature-info-item.wire-spec {
    width: 200px;
}

.pricing-sp-feature-info-item label {
    font-size: 12px;
    color: #666;
    margin-bottom: 5px;
}

.pricing-sp-feature-info-item span {
    font-weight: bold;
    color: #333;
}

/* 底部固定按钮 */
.pricing-sp-bottom-buttons {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    transform: none;
    display: flex;
    justify-content: center;
    gap: 12px;
    padding: 16px 0 0px 0;
    background: #fff;
    z-index: 1001;
}

/* 特征段组价页面 */
.pricing-sp-indicator-tabs {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
}

.pricing-sp-tab-button {
    padding: 0.5rem 1rem;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background: white;
    cursor: pointer;
    font-size: 0.875rem;
    color: rgba(0, 0, 0, 0.65);
    transition: all 0.3s ease;
}

.pricing-sp-tab-button:hover {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.pricing-sp-tab-button.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pricing-sp-indicator-panel {
    background: white;
    border-radius: 4px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
    padding: 1.5rem;
    margin-bottom: 1rem;
}

.pricing-sp-table-container {
    width: 100%;
    overflow-x: auto;
    position: relative;
    padding: 0;
    margin: 0;
    background: transparent;
}

.pricing-sp-indicator-table {
    width: 100%;
    border-collapse: collapse;
    margin: 0;
    table-layout: auto;
    background: #fff;
}

.pricing-sp-indicator-table td {
    padding: 8px 12px;
    border: 1px solid #e8e8e8;
    text-align: center;
    min-width: 100px;
}

.pricing-sp-indicator-table th {
    padding: 12px;
    background-color: #fafafa;
    border: 1px solid #e8e8e8;
    text-align: center;
    font-weight: bold;
    min-width: 100px;
    max-width: 200px;
    position: relative;
}

.pricing-sp-selected {
    background-color: #e6f7ff;
}

.pricing-sp-selected:hover {
    background-color: #e6f7ff;
}

.pricing-sp-empty-data {
    padding: 40px;
    text-align: center;
    color: #999;
    font-size: 14px;
}

/* 工作区布局 */
.pricing-ws-container {
    position: fixed;
    top: 56px;
    left: 0;
    right: 0;
    bottom: 0;
    overflow: hidden;
    background-color: var(--light-gray);
    padding: 0.5rem;
}

.pricing-ws-content {
    display: flex;
    width: 100%;
    height: 100%;
    gap: 0.5rem;
    transition: var(--transition);
}

.pricing-ws-panels-collapsed .pricing-ws-project-panel {
    width: 100%;
    transition: width 0.3s ease-in-out;
}

.pricing-ws-panels-collapsed .pricing-ws-right-panels {
    width: 0%;
    overflow: hidden;
    transition: width 0.3s ease-in-out;
}

.pricing-ws-panels-expanded .pricing-ws-project-panel {
    width: 45%;
    transition: width 0.3s ease-in-out;
}

.pricing-ws-panels-expanded .pricing-ws-right-panels {
    width: 55%;
    transition: width 0.3s ease-in-out;
}

.pricing-ws-project-panel {
    height: 100%;
    border: 1px solid var(--border-color);
    border-radius: 2px;
    background-color: #fff;
    box-shadow: var(--box-shadow);
    overflow: auto;
}

.pricing-ws-right-panels {
    height: 100%;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.pricing-ws-section-panel {
    height: 100%;
    border: 1px solid var(--border-color);
    border-radius: 2px;
    background-color: #fff;
    box-shadow: var(--box-shadow);
    overflow: auto;
}

/* 特征段组价-模态框 */
.pricing-sp-confirm-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1200;
}

.pricing-sp-confirm-modal.show {
    display: flex !important;
    align-items: center;
    justify-content: center;
}

.pricing-sp-confirm-content {
    position: relative;
    background: white;
    padding: 20px;
    border-radius: 8px;
    min-width: 400px;
    max-width: 500px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.pricing-sp-modal-title {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e8e8e8;
}

.pricing-sp-modal-buttons {
    margin-top: 20px;
    text-align: right;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

/* 历史指标查询 */
.pricing-sp-history-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    overflow-y: auto;
}

.pricing-sp-history-modal.show {
    display: block;
}

.pricing-sp-history-content {
    position: relative;
    margin: 30px auto;
    background: white;
    padding: 20px;
    border-radius: 8px;
    width: 90%;
    height: 100%;
    max-width: 1200px;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.pricing-sp-history-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e8e8e8;
}

.pricing-sp-history-title {
    font-size: 18px;
    font-weight: bold;
}

.pricing-sp-history-close {
    font-size: 24px;
    cursor: pointer;
    color: #999;
    transition: color 0.2s;
}

.pricing-sp-history-close:hover {
    color: #333;
}

.pricing-sp-modal-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid #e8e8e8;
}

/* 历史指标查询区域 */
/* 匹配度标签样式 - 添加与备份文件一致的样式，并添加前缀 */
.pricing-sp-match-tag {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 4px;
    background-color: #e6f3ff;
    color: #1890ff;
    font-size: 12px;
    font-weight: bold;
    min-width: 60px;
    text-align: center;
}



.pricing-sp-search-section {
    margin: 20px 0;
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 4px;
}

.pricing-sp-search-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    align-items: end;
}

.pricing-sp-search-item {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.pricing-sp-search-label {
    font-weight: 500;
    color: #333;
}

.pricing-sp-search-input {
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    width: 100%;
}

.pricing-sp-search-actions {
    display: flex;
    justify-content: flex-end;
    grid-column: 1 / -1;
}

/* 加载动画 */
.pricing-sp-loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

/* ================== 快速组价模块样式归档区 END ================== */ 

.pricing-shared-toast {
  display: block !important;
  opacity: 1 !important;
  visibility: visible !important;
  pointer-events: auto !important;
  z-index: 9999 !important;
} 

/* 组价汇总按钮样式 */
.pricing-pm-sum-btn {
  display: inline-block !important;
  visibility: visible !important;
  pointer-events: auto !important;
  margin-left: 10px;
}

/* 组价结果按钮样式 */
.pricing-share-result-btn {
  background-color: #52c41a !important;
  border-color: #52c41a !important;
  color: white !important;
}

.pricing-share-result-btn:hover {
  background-color: #389e0d !important;
  border-color: #389e0d !important;
  color: white !important;
}

/* 组价结果页面样式 */
.pricing-pm-result-content {
    width: 90%;
    max-width: 90vw;
    height: 90vh;
    margin: 5vh auto;
    padding: 2rem 2.5rem 1.5rem 2.5rem;
    display: flex;
    flex-direction: column;
    background: #fff;
    border-radius: 8px;
    overflow-y: auto;
    position: relative;
    box-shadow: var(--box-shadow);
}

.pricing-pm-modal-header {
    margin-bottom: 1rem;
}

.pricing-pm-header-title {
    display: flex;
    align-items: center;
    gap: 1rem; /* 标题与按钮间距 */
    /* justify-content: space-between; 删除使元素靠左 */
    width: 100%;
}

.pricing-pm-header-title h2 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
}

.pricing-pm-export-buttons {
    display: flex;
    gap: 10px;
}

.pricing-pm-result-table-container {
    overflow-x: auto;
    min-width: 1000px;
    max-height: 400px;
    border: 1px solid #eee;
    margin-bottom: 1rem;
    background: #fff;
}

.pricing-pm-result-table {
    min-width: 1000px;
    border-collapse: collapse;
    width: 100%;
}

.pricing-pm-project-info-panel {
    display: flex;
    gap: 2rem;
    align-items: center;
    margin-bottom: 1rem;
    font-weight: bold;
    background: #fafafa;
    padding: 1rem 1.5rem;
    border-radius: 4px;
}

.pricing-pm-project-info-item label,
.pricing-pm-project-info-item span {
    font-weight: bold;
    font-size: 1rem;
}

.pricing-pm-preview-tabs {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 0.5rem;
}

.pricing-pm-preview-tab {
    padding: 0.5rem 1rem;
    border: none;
    background: none;
    cursor: pointer;
    font-size: 1rem;
    color: rgba(0,0,0,0.65);
    position: relative;
    font-weight: 500;
}

.pricing-pm-preview-tab.active {
    color: var(--primary-color);
    font-weight: 700;
    border-bottom: 2px solid var(--primary-color);
}

/* 原先的导出按钮绝对定位已不需要 */
/* .pricing-pm-export-btn {
    position: absolute;
    top: 1.5rem;
    right: 2rem;
    z-index: 10;
} */

.pricing-pm-preview-panel {
    display: none;
}

.pricing-pm-preview-panel.active {
    display: block;
} 

.pricing-sp-section-title {
    font-size: 1.25rem;
    font-weight: 500;
    margin: 0 0 1rem 0;
    color: rgba(0, 0, 0, 0.85);
} 

/* === 特征段组价表格列宽与行高调整 START === */
.pricing-sp-indicator-table th,
.pricing-sp-indicator-table td {
    padding: 3px 8px; /* 进一步减小内边距使表格更紧凑 */
    font-size: 16px; /* 恢复字体大小为16px */
}

.pricing-sp-indicator-table th:first-child,
.pricing-sp-indicator-table td:first-child {
    min-width: 180px; /* 保持第一列宽度 */
    width: 180px;
}

/* 其他列减小宽度，使表格更紧凑 */
.pricing-sp-indicator-table th,
.pricing-sp-indicator-table td {
    min-width: 80px; /* 减小其他列的最小宽度 */
}

/* 使表格整体更紧凑 */
.pricing-sp-indicator-table {
    border-spacing: 0;
    border-collapse: collapse;
}
/* === 特征段组价表格列宽与行高调整 END === */ 

/* 特征段组价-价格管理模态框 */
.pricing-sp-price-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1200;
}

.pricing-sp-price-modal.show {
    display: flex !important;
    align-items: center;
    justify-content: center;
}

.pricing-sp-price-content {
    width: 800px;
    max-width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.pricing-sp-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 24px;
    border-bottom: 1px solid #f0f0f0;
}

.pricing-sp-modal-close {
    font-size: 20px;
    font-weight: 700;
    line-height: 1;
    cursor: pointer;
    opacity: 0.7;
}

.pricing-sp-modal-close:hover {
    opacity: 1;
}

.pricing-sp-price-body {
    padding: 24px;
    max-height: 60vh;
    overflow-y: auto;
}

.pricing-sp-modal-actions {
    padding: 16px 24px;
    text-align: right;
    border-top: 1px solid #f0f0f0;
}

.pricing-sp-modal-actions button {
    margin-left: 8px;
} 

.pricing-sp-price-modal .pricing-shared-input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    font-size: 14px;
    transition: all 0.3s;
}

.pricing-sp-price-modal .pricing-shared-input:focus {
    border-color: #40a9ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    outline: none;
}

.pricing-sp-price-modal .pricing-sp-indicator-table {
    width: 100%;
    border-collapse: collapse;
}

.pricing-sp-price-modal .pricing-sp-indicator-table th {
    background-color: #e6f7ff;
    font-weight: 500;
    text-align: left;
    padding: 12px 15px;
    border-bottom: 2px solid #1890ff;
    color: #1890ff;
}

.pricing-sp-price-modal .pricing-sp-indicator-table td {
    padding: 10px 15px;
    border-bottom: 1px solid #f0f0f0;
}

.pricing-sp-price-modal .pricing-sp-indicator-table tr:hover {
    background-color: #f9f9f9;
}

.pricing-sp-price-modal .pricing-shared-input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    font-size: 14px;
    transition: all 0.3s;
}

.pricing-sp-price-modal .pricing-shared-input:focus {
    border-color: #40a9ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    outline: none;
}

/* 设置列宽 */
.pricing-sp-price-modal .pricing-sp-indicator-table th:nth-child(1),
.pricing-sp-price-modal .pricing-sp-indicator-table td:nth-child(1) {
    width: 60%;
}

.pricing-sp-price-modal .pricing-sp-indicator-table th:nth-child(2),
.pricing-sp-price-modal .pricing-sp-indicator-table td:nth-child(2) {
    width: 15%;
    text-align: center;
}

.pricing-sp-price-modal .pricing-sp-indicator-table th:nth-child(3),
.pricing-sp-price-modal .pricing-sp-indicator-table td:nth-child(3) {
    width: 25%;
} 
