{% extends "base.html" %}

{% block title %}电网线路工程造价分析平台{% endblock %}

{% block head %}
    <style>
    .features-section {
        display: flex;
        justify-content: center;
        gap: 1.5rem;
        padding: 2rem 0;
        margin: 0 auto;
        max-width: 1600px;
    }
    
    .feature-card {
        flex: 0 1 280px;
        background: white;
        border-radius: 12px;
        padding: 1.5rem;
        box-shadow: var(--box-shadow);
        display: flex;
        flex-direction: column;
        gap: 1rem;
        transition: transform 0.3s ease;
        min-height: 340px; /* 统一高度，可根据实际内容调整 */
    }
    
    .feature-card:hover {
        transform: translateY(-5px);
    }
    
    .feature-icon {
        width: 3rem;
        height: 3rem;
        display: flex;
        align-items: center;
        justify-content: center;
        background: var(--primary-light);
        border-radius: 8px;
        color: var(--primary);
    }
    
    .feature-card h2 {
        font-size: 1.25rem;
        font-weight: 600;
        margin: 0;
    }
    
    .feature-card p {
        color: var(--text-color-secondary);
        margin: 0;
        font-size: 1rem;
        line-height: 1.5;
        flex-grow: 1;
    }
    
    .feature-sub-links {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
        margin-top: auto; /* 按钮推到底部 */
    }
    
    .feature-sub-links .btn {
        flex: 1;
        min-width: 120px;
        height: 44px;
        line-height: 44px;
        font-size: 1rem;
        padding: 0;
        text-align: center;
        border-radius: 22px;
        box-sizing: border-box;
    }
    
    @media (max-width: 1400px) {
        .features-section {
            flex-wrap: wrap;
            max-width: 1200px;
        }
        .feature-card {
            flex: 0 1 220px;
        }
    }
    
    @media (max-width: 1200px) {
        .features-section {
            max-width: 900px;
        }
    }
    
    @media (max-width: 768px) {
        .features-section {
            flex-direction: column;
            align-items: center;
            padding: 1rem;
        }
        
        .feature-card {
            width: 100%;
            max-width: 400px;
        }
    }
    </style>
{% endblock %}

{% block content %}
<!-- 功能卡片区 -->
<section class="features-section">
    <!-- 历史指标库 -->
    <div class="feature-card animate-fadeIn" style="animation-delay: 0.1s">
        <div class="feature-icon">
            <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
            </svg>
        </div>
        <h2>历史指标库</h2>
        <p>汇集输电线路工程历史指标数据，支持查询、对比与导出，助力造价参考与分析。</p>
        <div class="feature-sub-links">
            <a href="/his_ind/query" class="btn btn-primary">查看详情</a>
        </div>
    </div>

    <!-- 智能辅助校审 -->
    <div class="feature-card animate-fadeIn" style="animation-delay: 0.2s">
        <div class="feature-icon">
            <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"/>
            </svg>
        </div>
        <h2>智能辅助校审</h2>
        <p>上传工程概算文件，自动提取校审指标，生成校审报告，提升审核效率与准确性。</p>
        <div class="feature-sub-links">
            <a href="/audit" class="btn btn-primary">开始校审</a>
        </div>
        <!-- 新入口 -->
        <!-- 旧入口，暂时保留 -->
        <!-- <a href="/audit/project/workspace" class="btn btn-primary">开始校审（旧）</a> -->
        <!-- <a href="/audit/project/workspace" class="btn btn-secondary">工程管理</a>
        <a href="/audit/indicatorUpload" class="btn btn-secondary">数据上传</a> -->
    </div>

    <!-- 快速组价 -->
    <div class="feature-card animate-fadeIn" style="animation-delay: 0.3s">
        <div class="feature-icon">
            <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
        </div>
        <h2>快速组价</h2>
        <p>根据项目边界条件，组合历史指标，快速生成项目概算与量价汇总。</p>
        <div class="feature-sub-links">
            <a href="/quick_pricing/workspace" class="btn btn-primary">立即组价</a>
        </div>
        <!-- 旧入口，暂时保留   -->
        <!-- <a href="/quick_pricing/quickPricing" class="btn btn-secondary">旧版入口</a> -->
    </div>

    <!-- 规则管理 -->
    <!-- 规则管理暂时关闭 -->
    <!-- <div class="feature-card animate-fadeIn" style="animation-delay: 0.4s">
        <div class="feature-icon">
            <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
            </svg>
        </div>
        <h2>规则管理</h2>
        <p>维护和提炼校审规则，支持规则库管理与导出，保障校审科学性。</p>
        <div class="feature-sub-links">
            <a href="/ruleAudit.html" class="btn btn-primary">管理规则</a>
        </div>
    </div> -->

    <!-- 系统设置 -->
    <!-- 系统设置暂时关闭 -->
    <!-- <div class="feature-card animate-fadeIn" style="animation-delay: 0.5s">
        <div class="feature-icon">
            <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
            </svg>
        </div>
        <h2>系统设置</h2>
        <p>管理用户信息及平台系统参数，保障平台安全与高效运行。</p>
        <div class="feature-sub-links">
            <a href="/settings.html" class="btn btn-primary">系统设置</a>
        </div>
    </div> -->
</section>
{% endblock %}
