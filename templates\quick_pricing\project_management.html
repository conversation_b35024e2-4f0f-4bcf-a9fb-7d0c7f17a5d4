<!-- 工程列表 -->
<section class="pricing-pm-section">
    <!-- 导航栏 -->
    <div class="pricing-pm-panel-nav">
        <div class="pricing-pm-nav-left">
            <h2 class="pricing-pm-nav-title">工程管理</h2>
            <div class="pricing-pm-nav-breadcrumb">
                <span>快速组价</span>
                <i class="pricing-pm-nav-separator">/</i>
                <span class="current">工程管理</span>
            </div>
        </div>
        <div class="pricing-pm-nav-right">
            <span class="pricing-pm-nav-stats">共 <span id="projectCount">0</span> 个工程</span>
            <button type="button" class="pricing-shared-btn pricing-shared-btn-primary" onclick="showCreateProjectModal()">新建工程</button>
        </div>
    </div>

    <!-- 查询条件 -->
    <form class="pricing-pm-query-form" id="searchForm">
        <div class="pricing-pm-search-conditions">
            <div class="pricing-pm-form-row">
                <div class="pricing-pm-form-group">
                    <label>工程名称</label>
                    <input type="text" id="searchProjectName" placeholder="请输入工程名称">
                </div>
                <div class="pricing-pm-form-group voltage-group">
                    <label>电压等级</label>
                    <select id="searchVoltageLevel">
                        <option value="">全部</option>
                        <option value="500kV">500kV</option>
                        <option value="220kV">220kV</option>
                        <option value="110kV">110kV</option>
                    </select>
                </div>
                <div class="pricing-pm-form-group">
                    <label>线路总长度(km)</label>
                    <input type="number" id="searchLineLength" placeholder="请输入线路长度">
                </div>
                <div class="pricing-pm-form-group button-group-inline">
                    <label>&nbsp;</label>
                    <div class="pricing-pm-button-container">
                        <button type="button" class="pricing-shared-btn pricing-shared-btn-primary" onclick="searchProjects()">查询</button>
                        <button type="reset" class="pricing-shared-btn pricing-shared-btn-secondary">重置</button>
                    </div>
                </div>
            </div>
        </div>
    </form>

    <div class="pricing-pm-content-wrapper">
        <div class="pricing-pm-table-container">
            <table class="pricing-pm-table">
                <thead>
                    <tr>
                        <th>工程名称</th>
                        <th>电压等级</th>
                        <th>线路总长度</th>
                        <th>特征段数量</th>
                        <th>组价状态</th>
                        <th>创建时间</th>
                        <th>组价时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="projectTableBody">
                    <!-- 项目数据将通过JavaScript动态填充 -->
                    <template id="projectRowTemplate">
                        <tr>
                            <td data-field="工程名称"></td>
                            <td data-field="电压等级"></td>
                            <td data-field="线路总长度"></td>
                            <td data-field="特征段数量"></td>
                            <td><span class="pricing-pm-status-tag" data-field="组价状态"></span></td>
                            <td data-field="创建时间"></td>
                            <td data-field="组价时间"></td>
                            <td>
                                <button class="pricing-shared-btn pricing-shared-btn-primary" onclick="selectProject(this)" data-project-id="">特征段管理</button>
                                <button class="pricing-shared-btn pricing-shared-btn-primary pricing-pm-sum-btn" onclick="pricingSum(this)" data-project-id="">组价汇总</button>
                                <button class="pricing-shared-btn pricing-shared-btn-delete" onclick="deleteProject(this)" data-project-id="">删除</button>
                            </td>
                        </tr>
                    </template>
                </tbody>
            </table>
        </div>

        <div class="pricing-pm-pagination-wrapper">
            <div class="pricing-pm-pagination">
                <button class="active">1</button>
                <button>2</button>
                <button>3</button>
                <button>4</button>
                <button>5</button>
            </div>
        </div>
    </div>
</section>

<!-- 新建工程对话框 -->
<div id="createProjectModal" class="pricing-shared-modal">
    <div class="pricing-shared-modal-content">
        <span class="pricing-shared-modal-close" onclick="closeCreateProjectModal()">&times;</span>
        <h2>新建工程</h2>
        <form id="createProjectForm" class="pricing-pm-add-project-form">
            <div class="pricing-pm-form-column">
                <div class="pricing-pm-form-group">
                    <label>工程名称</label>
                    <input type="text" id="projectName" placeholder="请输入工程名称" required>
                </div>
                
                <div class="pricing-pm-form-group">
                    <label>电压等级</label>
                    <select id="voltageLevelNew">
                        <option value="500kV">500kV</option>
                        <option value="220kV">220kV</option>
                        <option value="110kV">110kV</option>
                    </select>
                </div>
                
                <div class="pricing-pm-form-group">
                    <label>线路总长度(km)</label>
                    <input type="number" id="lineLength" step="0.1" placeholder="请输入线路长度" required>
                </div>
                
                <div class="pricing-pm-form-group">
                    <label>备注</label>
                    <input type="text" id="projectRemark" placeholder="请输入备注信息">
                </div>
            </div>
            <div class="pricing-pm-form-actions">
                <button type="button" class="pricing-shared-btn pricing-shared-btn-primary" onclick="saveNewProject()">保存</button>
                <button type="button" class="pricing-shared-btn pricing-shared-btn-secondary" onclick="closeCreateProjectModal()">取消</button>
            </div>
        </form>
    </div>
</div>

<!-- 组价结果查看对话框 -->
<div id="projectPricingResultModal" class="pricing-shared-modal">
    <div class="pricing-shared-modal-content pricing-pm-result-content">
        <span class="pricing-shared-modal-close" onclick="closeProjectPricingResultModal()">&times;</span>
        <div class="pricing-pm-modal-header">
            <div class="pricing-pm-header-title">
                <h2>组价结果</h2>
                <div class="pricing-pm-export-buttons">
                    <button class="pricing-shared-btn pricing-shared-btn-primary pricing-pm-export-btn" onclick="exportProjectPricingResult()" data-project-id="">
                        <i class="fas fa-file-export"></i> 导出总算表（表一）
                    </button>
                    <button class="pricing-shared-btn pricing-shared-btn-primary pricing-pm-export-report-btn" onclick="exportProjectPricingReport()" data-project-id="">
                        <i class="fas fa-file-word"></i> 导出组价报告
                    </button>
                </div>
            </div>
        </div>
        <!-- 工程信息展示区域 -->
        <div class="pricing-pm-project-info-panel">
            <div class="pricing-pm-project-info-item">
                <label>工程名称</label>
                <span id="resultProjectName">500kV东莞西南部受电通道工程</span>
            </div>
            <div class="pricing-pm-project-info-item">
                <label>线路总长度(km)</label>
                <span id="resultLineLength">25</span>
            </div>
            <div class="pricing-pm-project-info-item">
                <label>电压等级</label>
                <span id="resultVoltageLevel">500kV</span>
            </div>
            <div class="pricing-pm-project-info-item">
                <label>特征段数量</label>
                <span id="resultSectionCount">3</span>
            </div>
        </div>
        <div class="pricing-pm-preview-tabs">
            <button class="pricing-pm-preview-tab active" id="zongjsTabBtn" onclick="switchProjectPricingTab('zongjs')">总算表（表一）</button>
            <button class="pricing-pm-preview-tab" id="bentiTabBtn" onclick="switchProjectPricingTab('benti')">本体费用用指标</button>
            <button class="pricing-pm-preview-tab" id="qitaTabBtn" onclick="switchProjectPricingTab('qita')">其他费用用指标</button>
        </div>
        <!-- 总算表面板 -->
        <div id="zongjsPanel" class="pricing-pm-preview-panel active">
            <div class="pricing-pm-result-table-container">
                <table class="pricing-pm-result-table">
                    <tbody>
                        <tr>
                            <td colspan="6" style="text-align: center; font-weight: bold;">架空线路工程总概算表</td>
                        </tr>
                        <tr>
                            <td colspan="3" style="text-align: left;">表一丙</td>
                            <td colspan="3" style="text-align: right;">金额单位：万元</td>
                        </tr>
                        <tr>
                            <th>序号</th>
                            <th>工程或费用名称</th>
                            <th>费用金额</th>
                            <th>各项占<br/>静态投资%</th>
                            <th>单位投资<br/>万元/km</th>
                            <th>费用编码</th>
                        </tr>
                        <tr>
                            <td>一</td>
                            <td>架空输电线路本体工程</td>
                            <td>7062</td>
                            <td>85.2</td>
                            <td>630.54</td>
                            <td></td>
                        </tr>
                        <tr>
                            <td>（一）</td>
                            <td>一般线路本体工程</td>
                            <td>7062</td>
                            <td>85.2</td>
                            <td>630.54</td>
                            <td></td>
                        </tr>
                        <tr>
                            <td>（二）</td>
                            <td>大跨越本体工程</td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                        </tr>
                        <tr>
                            <td>二</td>
                            <td>辅助设施工程</td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                        </tr>
                        <tr>
                            <td></td>
                            <td>小计</td>
                            <td>7062</td>
                            <td>85.2</td>
                            <td>630.54</td>
                            <td></td>
                        </tr>
                        <tr>
                            <td>三</td>
                            <td>编制基准期价差</td>
                            <td>1215</td>
                            <td>14.66</td>
                            <td>108.48</td>
                            <td>31840000000</td>
                        </tr>
                        <tr>
                            <td>四</td>
                            <td>设备购置费</td>
                            <td>12</td>
                            <td>0.14</td>
                            <td>1.07</td>
                            <td>318A0000000</td>
                        </tr>
                        <tr>
                            <td>五</td>
                            <td>其他费用</td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td>31830000000</td>
                        </tr>
                        <tr>
                            <td></td>
                            <td>其中：建设场地征用及清理费</td>
                            <td>2</td>
                            <td>0.02</td>
                            <td>0.18</td>
                            <td></td>
                        </tr>
                        <tr>
                            <td>六</td>
                            <td>基本预备费</td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td>318D0000000</td>
                        </tr>
                        <tr>
                            <td>七</td>
                            <td>特殊项目</td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td>31850000000</td>
                        </tr>
                        <tr>
                            <td></td>
                            <td>工程静态投资（一～七项合计）</td>
                            <td>8289</td>
                            <td>100</td>
                            <td>740.09</td>
                            <td>31860000000</td>
                        </tr>
                        <tr>
                            <td>八</td>
                            <td>动态费用</td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td>31870000000</td>
                        </tr>
                        <tr>
                            <td>（一）</td>
                            <td>价差预备费</td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td>31871000000</td>
                        </tr>
                        <tr>
                            <td>（二）</td>
                            <td>建设期贷款利息</td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td>31872000000</td>
                        </tr>
                        <tr>
                            <td></td>
                            <td>工程动态投资（一～八项合计）</td>
                            <td>8289</td>
                            <td></td>
                            <td>740.09</td>
                            <td>318C0000000</td>
                        </tr>
                        <tr>
                            <td></td>
                            <td>其中：可抵扣增值税额</td>
                            <td>887</td>
                            <td></td>
                            <td>79.2</td>
                            <td>318G0000000</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <!-- 本体费用面板 -->
        <div id="bentiPanel" class="pricing-pm-preview-panel">
            <div class="pricing-pm-result-table-container">
                <table class="pricing-pm-result-table">
                    <thead>
                        <tr>
                            <th>指标项</th>
                            <th>单位</th>
                            <th>指标量（总）</th>
                            <th>单价（元）</th>
                            <th>总价（万元）</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>铁塔基数</td>
                            <td>基</td>
                            <td>50</td>
                            <td>25000</td>
                            <td>125</td>
                        </tr>
                        <tr>
                            <td>直线塔</td>
                            <td>基</td>
                            <td>40</td>
                            <td>20000</td>
                            <td>80</td>
                        </tr>
                        <tr>
                            <td>耐张塔</td>
                            <td>基</td>
                            <td>10</td>
                            <td>45000</td>
                            <td>45</td>
                        </tr>
                        <tr>
                            <td>导线</td>
                            <td>t</td>
                            <td>30</td>
                            <td>35000</td>
                            <td>105</td>
                        </tr>
                        <tr>
                            <td>塔材</td>
                            <td>t</td>
                            <td>200</td>
                            <td>15000</td>
                            <td>300</td>
                        </tr>
                        <tr>
                            <td>基础钢材</td>
                            <td>t</td>
                            <td>15</td>
                            <td>12000</td>
                            <td>18</td>
                        </tr>
                        <tr>
                            <td>地脚螺栓和插入式角钢</td>
                            <td>t</td>
                            <td>5</td>
                            <td>15000</td>
                            <td>7.5</td>
                        </tr>
                        <tr>
                            <td>挂线金具</td>
                            <td>t</td>
                            <td>8</td>
                            <td>25000</td>
                            <td>20</td>
                        </tr>
                        <tr>
                            <td>导线间隔棒</td>
                            <td>套</td>
                            <td>100</td>
                            <td>500</td>
                            <td>5</td>
                        </tr>
                        <tr>
                            <td>防振锤</td>
                            <td>个</td>
                            <td>200</td>
                            <td>300</td>
                            <td>6</td>
                        </tr>
                        <tr>
                            <td>导线防振锤</td>
                            <td>个</td>
                            <td>150</td>
                            <td>300</td>
                            <td>4.5</td>
                        </tr>
                        <tr>
                            <td>地线防振锤</td>
                            <td>个</td>
                            <td>50</td>
                            <td>300</td>
                            <td>1.5</td>
                        </tr>
                        <tr>
                            <td>合成复合绝缘子</td>
                            <td>支</td>
                            <td>300</td>
                            <td>1000</td>
                            <td>30</td>
                        </tr>
                        <tr>
                            <td>玻璃绝缘子/盘式绝缘子</td>
                            <td>支</td>
                            <td>0</td>
                            <td>0</td>
                            <td>0</td>
                        </tr>
                        <tr>
                            <td>硬跳</td>
                            <td>套</td>
                            <td>50</td>
                            <td>2000</td>
                            <td>10</td>
                        </tr>
                        <tr>
                            <td>现浇混凝土</td>
                            <td>m³</td>
                            <td>1000</td>
                            <td>800</td>
                            <td>80</td>
                        </tr>
                        <tr>
                            <td>灌柱桩基础混凝土</td>
                            <td>m³</td>
                            <td>500</td>
                            <td>1000</td>
                            <td>50</td>
                        </tr>
                        <tr>
                            <td>基础护壁</td>
                            <td>m³</td>
                            <td>100</td>
                            <td>600</td>
                            <td>6</td>
                        </tr>
                        <tr>
                            <td>基础垫层</td>
                            <td>m³</td>
                            <td>50</td>
                            <td>500</td>
                            <td>2.5</td>
                        </tr>
                        <tr>
                            <td>钻孔灌注桩深度</td>
                            <td>m</td>
                            <td>800</td>
                            <td>1000</td>
                            <td>80</td>
                        </tr>
                        <tr>
                            <td>护坡、挡土墙</td>
                            <td>m³</td>
                            <td>120</td>
                            <td>1000</td>
                            <td>12</td>
                        </tr>
                        <tr>
                            <td>土方量</td>
                            <td>m³</td>
                            <td>5000</td>
                            <td>100</td>
                            <td>50</td>
                        </tr>
                        <tr>
                            <td>基坑土方（非机械）</td>
                            <td>m³</td>
                            <td>1500</td>
                            <td>200</td>
                            <td>30</td>
                        </tr>
                        <tr>
                            <td>基坑土方（机械）</td>
                            <td>m³</td>
                            <td>3500</td>
                            <td>150</td>
                            <td>52.5</td>
                        </tr>
                        <tr>
                            <td>接地槽</td>
                            <td>m³</td>
                            <td>150</td>
                            <td>400</td>
                            <td>6</td>
                        </tr>
                        <tr>
                            <td>排水沟</td>
                            <td>m³</td>
                            <td>100</td>
                            <td>500</td>
                            <td>5</td>
                        </tr>
                        <tr>
                            <td>尖峰、基面</td>
                            <td>m³</td>
                            <td>50</td>
                            <td>600</td>
                            <td>3</td>
                        </tr>
                        <tr>
                            <td>本体工程</td>
                            <td>万元</td>
                            <td>8000</td>
                            <td>-</td>
                            <td>8000</td>
                        </tr>
                        <tr>
                            <td>基础工程</td>
                            <td>万元</td>
                            <td>1500</td>
                            <td>-</td>
                            <td>1500</td>
                        </tr>
                        <tr>
                            <td>杆塔工程</td>
                            <td>万元</td>
                            <td>3200</td>
                            <td>-</td>
                            <td>3200</td>
                        </tr>
                        <tr>
                            <td>接地工程</td>
                            <td>万元</td>
                            <td>400</td>
                            <td>-</td>
                            <td>400</td>
                        </tr>
                        <tr>
                            <td>架线工程</td>
                            <td>万元</td>
                            <td>1600</td>
                            <td>-</td>
                            <td>1600</td>
                        </tr>
                        <tr>
                            <td>附件工程</td>
                            <td>万元</td>
                            <td>240</td>
                            <td>-</td>
                            <td>240</td>
                        </tr>
                        <tr>
                            <td>辅助工程</td>
                            <td>万元</td>
                            <td>160</td>
                            <td>-</td>
                            <td>160</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <!-- 其他费用面板 -->
        <div id="qitaPanel" class="pricing-pm-preview-panel">
            <div class="pricing-pm-result-table-container">
                <table class="pricing-pm-result-table">
                    <thead>
                        <tr>
                            <th>指标项</th>
                            <th>单位</th>
                            <th>指标量（总）</th>
                            <th>单价（元）</th>
                            <th>总价（万元）</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>项目建设管理费</td>
                            <td>万元</td>
                            <td>4000</td>
                            <td>-</td>
                            <td>4000</td>
                        </tr>
                        <tr>
                            <td>项目法人管理费</td>
                            <td>万元</td>
                            <td>2000</td>
                            <td>-</td>
                            <td>2000</td>
                        </tr>
                        <tr>
                            <td>招标费</td>
                            <td>万元</td>
                            <td>800</td>
                            <td>-</td>
                            <td>800</td>
                        </tr>
                        <tr>
                            <td>工程监理费</td>
                            <td>万元</td>
                            <td>1600</td>
                            <td>-</td>
                            <td>1600</td>
                        </tr>
                        <tr>
                            <td>施工过程造价咨询及竣工结算审核费</td>
                            <td>万元</td>
                            <td>400</td>
                            <td>-</td>
                            <td>400</td>
                        </tr>
                        <tr>
                            <td>工程保险费</td>
                            <td>万元</td>
                            <td>1200</td>
                            <td>-</td>
                            <td>1200</td>
                        </tr>
                        <tr>
                            <td>项目建设技术服务费</td>
                            <td>万元</td>
                            <td>240</td>
                            <td>-</td>
                            <td>240</td>
                        </tr>
                        <tr>
                            <td>项目前期工作费</td>
                            <td>万元</td>
                            <td>320</td>
                            <td>-</td>
                            <td>320</td>
                        </tr>
                        <tr>
                            <td>勘察设计费</td>
                            <td>万元</td>
                            <td>480</td>
                            <td>-</td>
                            <td>480</td>
                        </tr>
                        <tr>
                            <td>勘察费</td>
                            <td>万元</td>
                            <td>160</td>
                            <td>-</td>
                            <td>160</td>
                        </tr>
                        <tr>
                            <td>设计费</td>
                            <td>万元</td>
                            <td>320</td>
                            <td>-</td>
                            <td>320</td>
                        </tr>
                        <tr>
                            <td>基本设计费</td>
                            <td>万元</td>
                            <td>2400</td>
                            <td>-</td>
                            <td>2400</td>
                        </tr>
                        <tr>
                            <td>其他设计费</td>
                            <td>万元</td>
                            <td>800</td>
                            <td>-</td>
                            <td>800</td>
                        </tr>
                        <tr>
                            <td>设计文件评审费</td>
                            <td>万元</td>
                            <td>40</td>
                            <td>-</td>
                            <td>40</td>
                        </tr>
                        <tr>
                            <td>可行性研究文件评审费</td>
                            <td>万元</td>
                            <td>16</td>
                            <td>-</td>
                            <td>16</td>
                        </tr>
                        <tr>
                            <td>初步设计文件评审费</td>
                            <td>万元</td>
                            <td>12</td>
                            <td>-</td>
                            <td>12</td>
                        </tr>
                        <tr>
                            <td>施工图文件评审费</td>
                            <td>万元</td>
                            <td>12</td>
                            <td>-</td>
                            <td>12</td>
                        </tr>
                        <tr>
                            <td>工程建设检测费</td>
                            <td>万元</td>
                            <td>80</td>
                            <td>-</td>
                            <td>80</td>
                        </tr>
                        <tr>
                            <td>电力工程质量检测费</td>
                            <td>万元</td>
                            <td>60</td>
                            <td>-</td>
                            <td>60</td>
                        </tr>
                        <tr>
                            <td>桩基检测费</td>
                            <td>万元</td>
                            <td>20</td>
                            <td>-</td>
                            <td>20</td>
                        </tr>
                        <tr>
                            <td>电力工程技术经济标准编制费</td>
                            <td>万元</td>
                            <td>40</td>
                            <td>-</td>
                            <td>40</td>
                        </tr>
                        <tr>
                            <td>生产准备费</td>
                            <td>万元</td>
                            <td>160</td>
                            <td>-</td>
                            <td>160</td>
                        </tr>
                        <tr>
                            <td>管理车辆购置费</td>
                            <td>万元</td>
                            <td>10</td>
                            <td>-</td>
                            <td>10</td>
                        </tr>
                        <tr>
                            <td>工器具及办公家具购置费</td>
                            <td>万元</td>
                            <td>20</td>
                            <td>-</td>
                            <td>20</td>
                        </tr>
                        <tr>
                            <td>生产职工培训及提前进场费</td>
                            <td>万元</td>
                            <td>32</td>
                            <td>-</td>
                            <td>32</td>
                        </tr>
                        <tr>
                            <td>专业爆破服务费</td>
                            <td>万元</td>
                            <td>80</td>
                            <td>-</td>
                            <td>80</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script src="{{ url_for('static', filename='js/common.js') }}"></script>
<script src="{{ url_for('static', filename='js/project_management.js') }}"></script>

<script>
// 查看组价结果
function showProjectPricingResult(button) {
    // 获取项目ID
    const projectId = button.dataset.projectId;
    
    // 保存项目ID到模态框
    const exportBtn = document.querySelector('.pricing-pm-export-btn');
    if (exportBtn) {
        exportBtn.dataset.projectId = projectId;
    }
    
    // 保存项目ID到导出组价报告按钮
    const exportReportBtn = document.querySelector('.pricing-pm-export-report-btn');
    if (exportReportBtn) {
        exportReportBtn.dataset.projectId = projectId;
    }
    
    // 直接设置演示数据，不从表格行中获取
    const resultProjectName = document.getElementById('resultProjectName');
    const resultVoltageLevel = document.getElementById('resultVoltageLevel');
    const resultLineLength = document.getElementById('resultLineLength');
    const resultSectionCount = document.getElementById('resultSectionCount');
    
    // 检查元素是否存在，存在才设置内容
    if (resultProjectName) resultProjectName.textContent = '500kV东莞西南部受电通道工程';
    if (resultVoltageLevel) resultVoltageLevel.textContent = '500kV';
    if (resultLineLength) resultLineLength.textContent = '25';
    if (resultSectionCount) resultSectionCount.textContent = '3';
    
    // 显示模态框
    const modal = document.getElementById('projectPricingResultModal');
    if (modal) {
        modal.classList.add('show');
        
        // 默认显示总算表标签页
        switchProjectPricingTab('zongjs');
        
        // 设置表格容器的最大高度
        const resultTableContainers = document.querySelectorAll('.pricing-pm-result-table-container');
        resultTableContainers.forEach(container => {
            container.style.maxHeight = '400px';
        });
    } else {
        console.error('找不到组价结果模态框元素');
    }
}

// 关闭组价结果对话框
function closeProjectPricingResultModal() {
    const modal = document.getElementById('projectPricingResultModal');
    if (modal) {
        modal.classList.remove('show');
    }
}

// 切换组价结果tab
function switchProjectPricingTab(tabName) {
    // 切换tab按钮状态
    const bentiTab = document.getElementById('bentiTabBtn');
    const qitaTab = document.getElementById('qitaTabBtn');
    const zongjsTab = document.getElementById('zongjsTabBtn');
    if (tabName === 'benti') {
        bentiTab.classList.add('active');
        qitaTab.classList.remove('active');
        zongjsTab.classList.remove('active');
        document.getElementById('bentiPanel').classList.add('active');
        document.getElementById('qitaPanel').classList.remove('active');
        document.getElementById('zongjsPanel').classList.remove('active');
    } else if (tabName === 'qita') {
        bentiTab.classList.remove('active');
        qitaTab.classList.add('active');
        zongjsTab.classList.remove('active');
        document.getElementById('bentiPanel').classList.remove('active');
        document.getElementById('qitaPanel').classList.add('active');
        document.getElementById('zongjsPanel').classList.remove('active');
    } else if (tabName === 'zongjs') {
        bentiTab.classList.remove('active');
        qitaTab.classList.remove('active');
        zongjsTab.classList.add('active');
        document.getElementById('bentiPanel').classList.remove('active');
        document.getElementById('qitaPanel').classList.remove('active');
        document.getElementById('zongjsPanel').classList.add('active');
    }
}

// 导出组价结果
function exportProjectPricingResult() {
    // 获取当前项目ID
    const exportBtn = document.querySelector('.pricing-pm-export-btn');
    const projectId = exportBtn ? exportBtn.dataset.projectId : null;
    
    if (!projectId) {
        // 尝试从URL中获取项目ID
        const urlParams = new URLSearchParams(window.location.search);
        const idFromUrl = urlParams.get('project_id');
        
        if (idFromUrl) {
            downloadZongjs(idFromUrl);
        } else {
            if (typeof showToast === 'function') {
                showToast('无法获取工程ID，请重试', 'error');
            } else {
                alert('无法获取工程ID，请重试');
            }
        }
        return;
    }
    
    downloadZongjs(projectId);
}

// 导出组价报告
function exportProjectPricingReport() {
    // 获取当前项目ID
    const exportBtn = document.querySelector('.pricing-pm-export-report-btn');
    const projectId = exportBtn ? exportBtn.dataset.projectId : null;

    if (!projectId) {
        if (typeof showToast === 'function') {
            showToast('无法获取工程ID，请重试', 'error');
        } else {
            alert('无法获取工程ID，请重试');
        }
        return;
    }

    // 构建下载URL
    const downloadUrl = `/api/pricing/projects/${projectId}/export_report`;

    // 创建一个隐藏的a标签用于下载
    const downloadLink = document.createElement('a');
    downloadLink.href = downloadUrl;
    downloadLink.target = '_blank';
    downloadLink.download = '组价报告.docx'; // 示例文件名

    // 添加到DOM并触发点击
    document.body.appendChild(downloadLink);
    downloadLink.click();

    // 移除元素
    setTimeout(() => {
        document.body.removeChild(downloadLink);
    }, 100);

    if (typeof showToast === 'function') {
        showToast('组价报告已导出', 'success');
    } else {
        console.log('组价报告已导出');
        alert('组价报告已导出');
    }
}

// 下载总算表
function downloadZongjs(projectId) {
    // 构建下载URL
    const downloadUrl = `/api/pricing/projects/${projectId}/export_zongjs`;
    
    // 创建一个隐藏的a标签用于下载
    const downloadLink = document.createElement('a');
    downloadLink.href = downloadUrl;
    downloadLink.target = '_blank';
    downloadLink.download = '架空线路工程总算表(表一丙).xlsx';
    
    // 添加到DOM并触发点击
    document.body.appendChild(downloadLink);
    downloadLink.click();
    
    // 移除元素
    setTimeout(() => {
        document.body.removeChild(downloadLink);
    }, 100);
    
    if (typeof showToast === 'function') {
        showToast('总算表(表一丙)已导出', 'success');
    } else {
        console.log('总算表(表一丙)已导出');
        alert('总算表(表一丙)已导出');
    }
}

// 组价汇总函数
function pricingSum(button) {
    console.log('组价汇总函数被调用');
    // 显示开始组价的提示
    if (typeof showToast === 'function') {
        console.log('调用showToast显示开始汇总提示');
        showToast('工程将对所有特征段的组价结果进行汇总', 'info');
    } else {
        console.error('showToast函数未定义');
    }
    
    // 延迟2秒后显示完成提示并更新状态
    setTimeout(() => {
        const row = button.closest('tr');
        
        // 更新指标汇总状态
        const summaryStatusTag = row.querySelector('[data-field="指标汇总状态"]');
        if (summaryStatusTag) {
            summaryStatusTag.textContent = '已汇总';
            summaryStatusTag.className = 'pricing-pm-status-tag pricing-pm-status-success';
        }
        
        // 更新组价状态
        const statusTag = row.querySelector('[data-field="组价状态"]');
        statusTag.textContent = '已组价';
        statusTag.classList.add('pricing-pm-status-tag', 'pricing-pm-status-success');
        
        // 更改"组价汇总"按钮为"组价结果"按钮
        const pricingSumBtn = row.querySelector('.pricing-pm-sum-btn');
        if (pricingSumBtn) {
            pricingSumBtn.textContent = '组价结果';
            pricingSumBtn.onclick = function() {
                showProjectPricingResult(this);
            };
            // 更新按钮样式为绿色
            pricingSumBtn.classList.remove('pricing-shared-btn-primary');
            pricingSumBtn.classList.add('pricing-shared-btn-success', 'pricing-share-result-btn');
        }      
        // 调试输出
        console.log('setTimeout回调已执行，准备弹出toast');
        if (typeof showToast === 'function') {
            showToast('工程特征段组价汇总完成', 'success');
        } else {
            console.error('setTimeout回调中showToast函数未定义');
        }
    }, 2000);
}

// 初始化时设置状态标签和按钮显示
document.addEventListener('DOMContentLoaded', function() {
    // 检查showToast是否正确挂载到window对象
    console.log('DOMContentLoaded: 检查showToast函数', typeof window.showToast);
    if (typeof window.showToast !== 'function') {
        console.error('showToast函数未正确挂载到window对象！');
        // 重新定义showToast函数
        window.showToast = function(message, type) {
            console.log(`紧急备份showToast: [${type}] ${message}`);
            const toast = document.createElement('div');
            toast.className = `pricing-shared-toast ${type || 'info'}`;
            toast.textContent = message;
            toast.style.position = 'fixed';
            toast.style.top = '20px';
            toast.style.right = '20px';
            toast.style.zIndex = '9999';
            toast.style.padding = '12px 24px';
            toast.style.background = type === 'success' ? '#f6ffed' : '#e6f7ff';
            toast.style.border = `1px solid ${type === 'success' ? '#b7eb8f' : '#91d5ff'}`;
            toast.style.color = type === 'success' ? '#52c41a' : '#1890ff';
            toast.style.borderRadius = '4px';
            toast.style.boxShadow = '0 3px 6px rgba(0, 0, 0, 0.16)';
            toast.style.display = 'block';
            document.body.appendChild(toast);
            setTimeout(() => toast.remove(), 3000);
        };
        console.log('已重新定义showToast函数');
    } 
    
    const rows = document.querySelectorAll('#projectTableBody tr');
    rows.forEach(row => {
        const summaryStatus = row.querySelector('[data-field="指标汇总状态"]');
        const pricingStatus = row.querySelector('[data-field="组价状态"]');
        const viewButton = row.querySelector('.pricing-pm-view-pricing-btn');
        const pricingSumBtn = row.querySelector('.pricing-pm-sum-btn');
        
        // 检查指标汇总状态
        if (summaryStatus && summaryStatus.textContent === '已汇总') {
            summaryStatus.classList.add('pricing-pm-status-tag', 'pricing-pm-status-success');
        }
        
        // 检查组价状态
        if (pricingStatus && pricingStatus.textContent === '已组价') {
            pricingStatus.classList.add('pricing-pm-status-tag', 'pricing-pm-status-success');
            
            // 将"组价汇总"按钮更改为"组价结果"按钮
            if (pricingSumBtn) {
                pricingSumBtn.textContent = '组价结果';
                pricingSumBtn.onclick = function() {
                    showProjectPricingResult(this);
                };
                pricingSumBtn.classList.remove('pricing-shared-btn-primary');
                pricingSumBtn.classList.add('pricing-shared-btn-success', 'pricing-share-result-btn');
            }
        }
    });
});
</script> 