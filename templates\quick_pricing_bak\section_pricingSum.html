<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>特征段组价</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/pricing.css') }}">
    <style>
        /* 取消页面整体纵向滚动条，使页面无需上下滚动 */
        html, body {
            height: 100%;
            overflow: hidden;
        }
        /* 让页面主体充满高度，并在内部滚动 */
        body {
            display: flex;
            flex-direction: column;
        }

        .indicator-section {
            flex: 1 1 auto;
            overflow-y: auto;
        }

        /* 确认弹窗样式 */
        .modal-backdrop {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .modal-dialog {
            background: white;
            border-radius: 4px;
            padding: 20px;
            min-width: 400px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .modal-header {
            margin-bottom: 16px;
        }

        .modal-header h3 {
            margin: 0;
            font-size: 16px;
            font-weight: bold;
        }

        .modal-body {
            margin-bottom: 20px;
        }

        .modal-footer {
            display: flex;
            justify-content: flex-end;
            gap: 8px;
        }

        /* 模态框样式 */
        #confirmModal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }

        .modal-content {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 20px;
            border-radius: 4px;
            min-width: 400px;
        }

        .modal-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 15px;
        }

        .modal-buttons {
            margin-top: 20px;
            text-align: right;
        }

        .modal-buttons button {
            margin-left: 10px;
        }

        /* 按钮样式 */
        .btn {
            padding: 12px 24px;
            border: 1px solid transparent;
            border-radius: 980px;
            font-size: 0.875rem;
            cursor: pointer;
            line-height: 1;
            white-space: nowrap;
        }

        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .btn-secondary {
            background-color: #fff;
            border-color: #d9d9d9;
            color: rgba(0, 0, 0, 0.65);
        }
        
        /* 标题加粗样式 */
        .page-title, .section-title {
            font-weight: bold !important;
        }
        
        /* 为编辑模式添加样式 */
        .editable-cell {
            background-color: #f8f8f8;
            border: 1px solid #ddd;
            padding: 3px;
        }
        
        /* 其他按钮样式保持一致 */
        .btn-manage {
            margin-left: 10px;
        }
        
        /* 历史指标查询弹出层样式 */
        #historyIndicatorModal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            overflow-y: auto;
        }
        
        .history-modal-content {
            position: relative;
            margin: 30px auto;
            background: white;
            padding: 20px;
            border-radius: 4px;
            width: 90%;
            max-width: 1200px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
        
        .history-modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #e8e8e8;
        }
        
        .history-modal-title {
            font-size: 18px;
            font-weight: bold;
        }
        
        .history-modal-close {
            font-size: 20px;
            cursor: pointer;
            color: #999;
        }
        
        .history-modal-body {
            max-height: 70vh;
            overflow-y: auto;
        }
        
        .history-modal-footer {
            margin-top: 20px;
            padding-top: 10px;
            border-top: 1px solid #e8e8e8;
            text-align: right;
        }

        /* 确认对话框样式 */
        #confirmModal {
            display: none;
            position: fixed;
            z-index: 1100;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
        }
        
        #confirmModal .modal-content {
            background-color: #fff;
            margin: 15% auto;
            padding: 20px;
            border-radius: 5px;
            width: 400px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }
        
        #confirmModal .modal-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
        }
        
        #confirmModal .modal-buttons {
            display: flex;
            justify-content: flex-end;
            margin-top: 20px;
        }
        
        #confirmModal .modal-buttons button {
            margin-left: 10px;
        }

        /* 新增底部按钮固定定位样式 */
        .bottom-buttons {
            position: fixed;
            bottom: 0;
            left: 0;
            width: 100%;
            transform: none;
            display: flex;
            justify-content: center;
            gap: 12px;
            padding: 16px 0 0px 0;
            background: #fff;
            z-index: 1001;
        }

        /* 单价管理模态框遮罩样式 */
        #priceManagerModal {
            display: none;
            position: fixed;
            z-index: 1100;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            overflow-y: auto;
            /* 使用 flex 居中内部内容 */
            display: flex;
            justify-content: center;
            align-items: center;
        }

        #priceManagerModal .modal-content {
            position: relative; /* 相对居中容器内部 */
            max-height: 90vh;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="indicator-section">
    <div class="page-header">
            <h1 class="page-title">特征段组价</h1>
            <!-- 注释掉右上角按钮
        <div class="page-actions">
                <button type="button" class="btn btn-secondary" onclick="window.parent.closeIndicatorSelectModal()">返回</button>
                <button type="button" class="btn btn-primary" onclick="saveIndicatorSelection()">结果保存</button>
        </div>
            -->
    </div>

    <!-- 特征段信息 -->
        <div class="feature-info">
            <div class="feature-info-item name">
                <label>特征段名称</label>
                <span id="sectionName">-</span>
            </div>
            <div class="feature-info-item length">
                <label>线路长度</label>
                <span id="lineLength">-</span>
            </div>
            <div class="feature-info-item wind-speed">
                <label>风速</label>
                <span id="windSpeed">-</span>
            </div>
            <div class="feature-info-item ice-thickness">
                <label>覆冰</label>
                <span id="iceThickness">-</span>
            </div>
            <div class="feature-info-item circuit-count">
                <label>回路数</label>
                <span id="circuitCount">-</span>
            </div>
            <div class="feature-info-item wire-spec">
                <label>导线规格</label>
                <span id="wireSpec">-</span>
            </div>
        </div>

        <!-- 特征段基准指标量区域 -->
        <div class="indicator-section section-card">
            <h2 class="section-title">组价计算</h2>
            <div class="tab-header">
                <div class="indicator-tabs">
                    <button class="tab-button active" onclick="switchTab('benti', 'base')">本体费用指标</button>
                    <button class="tab-button" onclick="switchTab('qita', 'base')">其他费用指标</button>
                    <button class="btn btn-primary" onclick="showHistoryIndicatorModal()">基准指标创建</button>
                    <button class="btn btn-primary btn-edit" onclick="editBaseIndicator()">基准指标编辑</button>
                    <button class="btn btn-primary" onclick="calculateBaseTotal()">指标总量计算</button>
                    <button class="btn btn-primary btn-manage" onclick="managePrice()">单价管理</button>
                    <button class="btn btn-primary" onclick="calculatePrice()">组价计算</button>
        </div>
    </div>

            <!-- 本体费用基准指标面板 -->
            <div id="bentiPanel-base" class="indicator-panel">
                <div class="table-container">
                    <table class="indicator-table">
                        <thead>
                            <tr>
                                <th>数量|价格</th>
                                <th>铁塔基数(基)</th>
                                <th>直线塔(基)</th>
                                <th>耐张塔(基)</th>
                                <th>耐张比例(%)</th>
                                <th>导线(t)</th>
                                <th>塔材(t)</th>
                                <th>基础钢材(t)</th>
                                <th>地脚螺栓和插入式角钢(t)</th>
                                <th>挂线金具(t)</th>
                                <th>导线间隔棒(套)</th>
                                <th>防振锤(个)</th>
                                <th>导线防振锤(个)</th>
                                <th>地线防振锤(个)</th>
                                <th>合成复合绝缘子(支)</th>
                                <th>玻璃绝缘子/盘式绝缘子(支)</th>
                                <th>硬跳(套)</th>
                                <th>现浇混凝土(m³)</th>
                                <th>灌柱桩基础混凝土(m³)</th>
                                <th>基础护壁(m³)</th>
                                <th>基础垫层(m³)</th>
                                <th>钻孔灌注桩深度(m)</th>
                                <th>护坡、挡土墙(m³)</th>
                                <th>土方量(m³)</th>
                                <th>基坑土方（非机械）(m³)</th>
                                <th>基坑土方（机械）(m³)</th>
                                <th>接地槽(m³)</th>
                                <th>排水沟(m³)</th>
                                <th>尖峰、基面(m³)</th>
                                <th>本体工程(万元)</th>
                                <th>基础工程(万元)</th>
                                <th>杆塔工程(万元)</th>
                                <th>接地工程(万元)</th>
                                <th>架线工程(万元)</th>
                                <th>附件工程(万元)</th>
                                <th>辅助工程(万元)</th>
                                <th>单价(元)</th>
                            </tr>
                        </thead>
                        <tbody id="bentiBaseTableBody">
                            <!-- 默认显示四行数据 -->
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- 其他费用基准指标面板 -->
            <div id="qitaPanel-base" class="indicator-panel" style="display: none;">
                <div class="table-container">
                    <table class="indicator-table">
                        <thead>
                            <tr>
                                <th>数量|价格</th>
                                <th>项目建设管理费（万元）</th>
                                <th>项目法人管理费（万元）</th>
                                <th>招标费（万元）</th>
                                <th>工程监理费（万元）</th>
                                <th>施工过程造价咨询及竣工结算审核费（万元）</th>
                                <th>工程保险费（万元）</th>
                                <th>项目建设技术服务费（万元）</th>
                                <th>项目前期工作费（万元）</th>
                                <th>勘察设计费（万元）</th>
                                <th>勘察费（万元）</th>
                                <th>设计费（万元）</th>
                                <th>基本设计费（万元）</th>
                                <th>其他设计费（万元）</th>
                                <th>设计文件评审费（万元）</th>
                                <th>可行性研究文件评审费（万元）</th>
                                <th>初步设计文件评审费（万元）</th>
                                <th>施工图文件评审费（万元）</th>
                                <th>工程建设检测费（万元）</th>
                                <th>电力工程质量检测费（万元）</th>
                                <th>桩基检测费（万元）</th>
                                <th>电力工程技术经济标准编制费（万元）</th>
                                <th>生产准备费（万元）</th>
                                <th>管理车辆购置费（万元）</th>
                                <th>工器具及办公家具购置费（万元）</th>
                                <th>生产职工培训及提前进场费（万元）</th>
                                <th>专业爆破服务费（万元）</th>
                                <th>单价(元)</th>
                            </tr>
                        </thead>
                        <tbody id="qitaBaseTableBody">
                            <!-- 默认显示四行数据 -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- 加载状态遮罩 -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner"></div>
    </div>

    <!-- 新的底部按钮 -->
    <div class="bottom-buttons">
        <button type="button" class="btn btn-outline-secondary" onclick="closeModal()">返回</button>
        <button type="button" class="btn btn-primary" onclick="saveIndicatorSelection()">结果保存</button>
    </div>

    <!-- 创建基准指标确认框 -->
    <div id="confirmModal">
        <div class="modal-content">
            <div class="modal-title">创建基准指标</div>
            <div>是否将所选历史指标数据创建为基准指标，如所选历史指标为多条，将进行加权平均做为基准指标。</div>
            <div class="modal-buttons">
                <button class="btn btn-secondary" onclick="hideModal()">取消</button>
                <button class="btn btn-primary" onclick="confirmModal()">确定</button>
            </div>
        </div>
    </div>
    
    <!-- 历史指标查询弹出层 -->
    <div id="historyIndicatorModal">
        <div class="history-modal-content">
            <div class="history-modal-header">
                <div class="history-modal-title">历史指标查询</div>
                <div class="history-modal-close" onclick="hideHistoryIndicatorModal()">×</div>
            </div>
            <div class="history-modal-body">
                <!-- 查询条件区域 -->
                <div class="search-section">
                    <h2 class="section-title">查询条件</h2>
                    <div class="search-grid">
                        <div class="search-item">
                            <label class="search-label">线路长度(km)</label>
                            <input type="number" id="searchLineLength" class="search-input" placeholder="请输入线路长度">
                        </div>
                        <div class="search-item">
                            <label class="search-label">风速(m/s)</label>
                            <input type="number" id="searchWindSpeed" class="search-input" placeholder="请输入风速">
                        </div>
                        <div class="search-item">
                            <label class="search-label">覆冰(mm)</label>
                            <input type="number" id="searchIceThickness" class="search-input" placeholder="请输入覆冰厚度">
                        </div>
                        <div class="search-item">
                            <label class="search-label">回路数</label>
                            <select id="searchCircuitCount" class="search-input">
                                <option value="">全部</option>
                                <option value="单回路">单回路</option>
                                <option value="双回路">双回路</option>
                            </select>
                        </div>
                        <div class="search-item">
                            <label class="search-label">导线规格</label>
                            <input type="text" id="searchWireSpec" class="search-input" placeholder="请输入导线规格">
                        </div>
                        <div class="search-actions">
                            <button type="button" class="btn btn-primary" onclick="searchIndicators()">查询</button>
                        </div>
                    </div>
                </div>
                
                <!-- 查询结果标签页 -->
                <div class="tab-header">
                    <div class="indicator-tabs">
                        <button class="tab-button active" onclick="switchTab('benti', 'history')">本体费用指标</button>
                        <button class="tab-button" onclick="switchTab('qita', 'history')">其他费用指标</button>
                    </div>
    </div>

    <!-- 本体费用指标面板 -->
                <div id="bentiPanel-history" class="indicator-panel">
        <div class="table-container">
            <table class="indicator-table">
                <thead>
                    <tr>
                        <th>选择</th>
                                    <th>匹配度</th>
                                    <th>工程名称</th>
                                    <th>线路工程</th>
                                    <th>线路总长度(km)</th>
                                    <th>回路数</th>
                        <th>风速(m/s)</th>
                        <th>覆冰(mm)</th>
                                    <th>导线规格</th>
                                    <th>平地(%)</th>
                                    <th>丘陵(%)</th>
                                    <th>山地(%)</th>
                                    <th>高山(%)</th>
                                    <th>峻岭(%)</th>
                                    <th>泥沼(%)</th>
                                    <th>河网(%)</th>
                                    <th>人力运距(km)</th>
                                    <th>汽车运距（含拖拉机）(km)</th>
                                    <th>铁塔基数(基)</th>
                                    <th>直线塔(基)</th>
                                    <th>耐张塔(基)</th>
                                    <th>耐张比例(%)</th>
                                    <th>导线(t)</th>
                                    <th>塔材(t)</th>
                                    <th>基础钢材(t)</th>
                                    <th>地脚螺栓和插入式角钢(t)</th>
                                    <th>挂线金具(t)</th>
                                    <th>导线间隔棒(套)</th>
                                    <th>防振锤(个)</th>
                                    <th>导线防振锤(个)</th>
                                    <th>地线防振锤(个)</th>
                                    <th>合成/复合绝缘子(支)</th>
                                    <th>玻璃绝缘子/盘式绝缘子(支)</th>
                                    <th>硬跳(套)</th>
                                    <th>现浇混凝土(m³)</th>
                                    <th>灌柱桩基础混凝土(m³)</th>
                                    <th>基础护壁(m³)</th>
                                    <th>基础垫层(m³)</th>
                                    <th>钻孔灌注桩深度(m)</th>
                                    <th>护坡、挡土墙(m³)</th>
                                    <th>土方量(m³)</th>
                                    <th>基坑土方（非机械）(m³)</th>
                                    <th>基坑土方（机械）(m³)</th>
                                    <th>接地槽(m³)</th>
                                    <th>排水沟(m³)</th>
                                    <th>尖峰、基面(m³)</th>
                                    <th>本体工程(万元)</th>
                                    <th>基础工程(万元)</th>
                                    <th>杆塔工程(万元)</th>
                                    <th>接地工程(万元)</th>
                                    <th>架线工程(万元)</th>
                                    <th>附件工程(万元)</th>
                                    <th>辅助工程(万元)</th>
                    </tr>
                </thead>
                <tbody id="bentiTableBody">
                    <!-- 本体费用指标数据将通过JavaScript动态填充 -->
                </tbody>
            </table>
                        <div class="empty-data" id="bentiEmptyData" style="display: none;">
                            暂无匹配的本体费用指标数据
                        </div>
        </div>
    </div>

    <!-- 其他费用指标面板 -->
                <div id="qitaPanel-history" class="indicator-panel" style="display: none;">
        <div class="table-container">
            <table class="indicator-table">
                <thead>
                    <tr>
                        <th>选择</th>
                        <th>匹配度</th>
                                    <th>工程名称</th>
                                    <th>线路工程</th>
                                    <th>合并气象区总长度</th>
                                    <th>项目建设管理费（万元）</th>
                                    <th>项目法人管理费（万元）</th>
                                    <th>招标费（万元）</th>
                                    <th>工程监理费（万元）</th>
                                    <th>施工过程造价咨询及竣工结算审核费（万元）</th>
                                    <th>工程保险费（万元）</th>
                                    <th>项目建设技术服务费（万元）</th>
                                    <th>项目前期工作费（万元）</th>
                                    <th>勘察设计费（万元）</th>
                                    <th>勘察费（万元）</th>
                                    <th>设计费（万元）</th>
                                    <th>基本设计费（万元）</th>
                                    <th>其他设计费（万元）</th>
                                    <th>设计文件评审费（万元）</th>
                                    <th>可行性研究文件评审费（万元）</th>
                                    <th>初步设计文件评审费（万元）</th>
                                    <th>施工图文件评审费（万元）</th>
                                    <th>工程建设检测费（万元）</th>
                                    <th>电力工程质量检测费（万元）</th>
                                    <th>桩基检测费（万元）</th>
                                    <th>电力工程技术经济标准编制费（万元）</th>
                                    <th>生产准备费（万元）</th>
                                    <th>管理车辆购置费（万元）</th>
                                    <th>工器具及办公家具购置费（万元）</th>
                                    <th>生产职工培训及提前进场费（万元）</th>
                                    <th>专业爆破服务费（万元）</th>
                    </tr>
                </thead>
                <tbody id="qitaTableBody">
                    <!-- 其他费用指标数据将通过JavaScript动态填充 -->
                </tbody>
            </table>
                        <div class="empty-data" id="qitaEmptyData" style="display: none;">
                            暂无匹配的其他费用指标数据
        </div>
    </div>
                </div>
            </div>
            <div class="history-modal-footer">
                <button class="btn btn-secondary" onclick="hideHistoryIndicatorModal()">取消</button>
                <button class="btn btn-primary" id="saveHistoryBtn" onclick="confirmCreateBaseIndicator()">保存</button>
            </div>
        </div>
    </div>

    <!-- 单价管理模态框 -->
    <div id="priceManagerModal" style="display: none;">
        <div class="modal-content" style="width: 80%; max-width: 800px;">
            <div class="modal-title">单价管理</div>
            <div class="modal-body" style="max-height: 500px; overflow-y: auto;">
                <div class="tab-header">
                    <div class="indicator-tabs">
                        <button class="tab-button active" onclick="switchTab('benti', 'price')">本体费用指标单价</button>
                        <button class="tab-button" onclick="switchTab('qita', 'price')">其他费用指标单价</button>
                    </div>
                </div>
                
                <!-- 本体费用单价管理 -->
                <div id="bentiPanel-price" class="indicator-panel">
                    <div class="table-container">
                        <table class="indicator-table">
                            <thead>
                                <tr>
                                    <th>指标名称</th>
                                    <th>单位</th>
                                    <th>单价(元)</th>
                                </tr>
                            </thead>
                            <tbody id="bentiPriceTableBody">
                                <!-- 单价数据将通过JavaScript加载 -->
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <!-- 其他费用单价管理 -->
                <div id="qitaPanel-price" class="indicator-panel" style="display: none;">
                    <div class="table-container">
                        <table class="indicator-table">
                            <thead>
                                <tr>
                                    <th>指标名称</th>
                                    <th>单位</th>
                                    <th>单价(元)</th>
                                </tr>
                            </thead>
                            <tbody id="qitaPriceTableBody">
                                <!-- 单价数据将通过JavaScript加载 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="modal-buttons">
                <button class="btn btn-secondary" onclick="closePriceModal()">取消</button>
                <button class="btn btn-primary" onclick="savePriceSettings()">保存</button>
            </div>
        </div>
    </div>

    <script>
        // 获取工程ID和特征段ID
        let projectId, sectionId;

        // 当前选择的指标
        let selectedBentiId = null;
        let selectedQitaId = null;

        // 初始化选择ID数组
        let selectedBentiIds = [];
        let selectedQitaIds = [];

        // 显示/隐藏加载状态
        function toggleLoading(show) {
            document.getElementById('loadingOverlay').style.display = show ? 'flex' : 'none';
        }

        // 显示提示信息
        function showToast(message, type = 'success') {
            const toast = document.createElement('div');
            toast.className = `toast ${type}`;
            toast.textContent = message;
            document.body.appendChild(toast);
            
            setTimeout(() => {
                toast.remove();
            }, 3000);
        }

        // 初始化函数，供父页面调用
        window.initIndicatorSelect = function(pid, sid) {
            projectId = pid;
            sectionId = sid;
            loadSectionInfo();
            
            // 绑定查询按钮点击事件
            document.querySelector('.search-actions button').onclick = searchIndicators;
        };

        // 加载特征段信息
        async function loadSectionInfo() {
            toggleLoading(true);
            try {
                const response = await fetch(`/api/pricing/projects/${projectId}/feature_sections/${sectionId}`);
                if (!response.ok) {
                    throw new Error('加载特征段信息失败');
                }
                
                const section = await response.json();
                
                // 填充特征段信息
                document.getElementById('sectionName').textContent = section.特征段名称 || '-';
                document.getElementById('lineLength').textContent = section.线路长度 ? `${section.线路长度}km` : '-';
                document.getElementById('windSpeed').textContent = section.边界条件?.风速 ? `${section.边界条件.风速}m/s` : '-';
                document.getElementById('iceThickness').textContent = section.边界条件?.覆冰 ? `${section.边界条件.覆冰}mm` : '-';
                document.getElementById('circuitCount').textContent = section.边界条件?.回路数 || '-';
                document.getElementById('wireSpec').textContent = section.边界条件?.导线规格 || '-';
                
                // 记录已选择的指标
                selectedBentiId = section.本体指标序号;
                selectedQitaId = section.其他指标序号;
                
                // 加载指标数据
                await loadIndicators();
            } catch (error) {
                console.error('加载特征段信息失败:', error);
                showToast('加载特征段信息失败', 'error');
            } finally {
                toggleLoading(false);
            }
        }

        // 演示数据
        const demoData = {
            benti: [
                {
                    "序号": 1,
                    "工程名称": "某500kV输变电工程",
                    "线路工程": "500kV某线路工程",
                    "线路总长度": 50.5,
                    "回路数": "双回路",
                    "风速": 35,
                    "覆冰": 15,
                    "导线规格": "4×JL/G1A-400/35",
                    "平地": 30,
                    "丘陵": 40,
                    "山地": 20,
                    "高山": 10,
                    "峻岭": 0,
                    "泥沼": 0,
                    "河网": 0,
                    "人力运距": 2.5,
                    "汽车运距": 15,
                    "铁塔基数": 120,
                    "直线塔": 90,
                    "耐张塔": 30,
                    "耐张比例": 25,
                    "导线": 150,
                    "塔材": 800,
                    "基础钢材": 60,
                    "地脚螺栓和插入式角钢": 20,
                    "挂线金具": 25,
                    "导线间隔棒": 240,
                    "防振锤": 480,
                    "导线防振锤": 360,
                    "地线防振锤": 120,
                    "合成复合绝缘子": 720,
                    "玻璃绝缘子盘式绝缘子": 0,
                    "硬跳": 120,
                    "现浇混凝土": 3600,
                    "灌柱桩基础混凝土": 1200,
                    "基础护壁": 180,
                    "基础垫层": 120,
                    "钻孔灌注桩深度": 1800,
                    "护坡挡土墙": 240,
                    "土方量": 12000,
                    "基坑土方非机械": 3600,
                    "基坑土方机械": 8400,
                    "接地槽": 360,
                    "排水沟": 240,
                    "尖峰基面": 120,
                    "本体工程": 8000,
                    "基础工程": 2400,
                    "杆塔工程": 3200,
                    "接地工程": 400,
                    "架线工程": 1600,
                    "附件工程": 240,
                    "辅助工程": 160
                },
                {
                    "序号": 2,
                    "工程名称": "某220kV输变电工程",
                    "线路工程": "220kV某线路工程",
                    "线路总长度": 30.2,
                    "回路数": "双回路",
                    "风速": 30,
                    "覆冰": 10,
                    "导线规格": "2×JL/G1A-300/25",
                    "平地": 50,
                    "丘陵": 30,
                    "山地": 20,
                    "高山": 0,
                    "峻岭": 0,
                    "泥沼": 0,
                    "河网": 0,
                    "人力运距": 2,
                    "汽车运距": 12,
                    "铁塔基数": 80,
                    "直线塔": 64,
                    "耐张塔": 16,
                    "耐张比例": 20,
                    "导线": 90,
                    "塔材": 480,
                    "基础钢材": 36,
                    "地脚螺栓和插入式角钢": 12,
                    "挂线金具": 15,
                    "导线间隔棒": 160,
                    "防振锤": 320,
                    "导线防振锤": 240,
                    "地线防振锤": 80,
                    "合成复合绝缘子": 480,
                    "玻璃绝缘子盘式绝缘子": 0,
                    "硬跳": 80,
                    "现浇混凝土": 2400,
                    "灌柱桩基础混凝土": 800,
                    "基础护壁": 120,
                    "基础垫层": 80,
                    "钻孔灌注桩深度": 1200,
                    "护坡挡土墙": 160,
                    "土方量": 8000,
                    "基坑土方非机械": 2400,
                    "基坑土方机械": 5600,
                    "接地槽": 240,
                    "排水沟": 160,
                    "尖峰基面": 80,
                    "本体工程": 5000,
                    "基础工程": 1500,
                    "杆塔工程": 2000,
                    "接地工程": 250,
                    "架线工程": 1000,
                    "附件工程": 150,
                    "辅助工程": 100
                }
            ],
            qita: [
                {
                    "序号": 1,
                    "工程名称": "某500kV输变电工程",
                    "线路工程": "500kV某线路工程",
                    "合并气象区总长度": 50.5,
                    "项目建设管理费": 400,
                    "项目法人管理费": 200,
                    "招标费": 80,
                    "工程监理费": 160,
                    "施工过程造价咨询及竣工结算审核费": 40,
                    "工程保险费": 120,
                    "项目建设技术服务费": 240,
                    "项目前期工作费": 320,
                    "勘察设计费": 480,
                    "勘察费": 160,
                    "设计费": 320,
                    "基本设计费": 240,
                    "其他设计费": 80,
                    "设计文件评审费": 40,
                    "可行性研究文件评审费": 16,
                    "初步设计文件评审费": 12,
                    "施工图文件评审费": 12,
                    "工程建设检测费": 80,
                    "电力工程质量检测费": 60,
                    "桩基检测费": 20,
                    "电力工程技术经济标准编制费": 40,
                    "生产准备费": 160,
                    "管理车辆购置费": 80,
                    "工器具及办公家具购置费": 40,
                    "生产职工培训及提前进场费": 32,
                    "专业爆破服务费": 8
                },
                {
                    "序号": 2,
                    "工程名称": "某220kV输变电工程",
                    "线路工程": "220kV某线路工程",
                    "合并气象区总长度": 30.2,
                    "项目建设管理费": 240,
                    "项目法人管理费": 120,
                    "招标费": 48,
                    "工程监理费": 96,
                    "施工过程造价咨询及竣工结算审核费": 24,
                    "工程保险费": 72,
                    "项目建设技术服务费": 144,
                    "项目前期工作费": 192,
                    "勘察设计费": 288,
                    "勘察费": 96,
                    "设计费": 192,
                    "基本设计费": 144,
                    "其他设计费": 48,
                    "设计文件评审费": 24,
                    "可行性研究文件评审费": 10,
                    "初步设计文件评审费": 7,
                    "施工图文件评审费": 7,
                    "工程建设检测费": 48,
                    "电力工程质量检测费": 36,
                    "桩基检测费": 12,
                    "电力工程技术经济标准编制费": 24,
                    "生产准备费": 96,
                    "管理车辆购置费": 48,
                    "工器具及办公家具购置费": 24,
                    "生产职工培训及提前进场费": 19,
                    "专业爆破服务费": 5
                }
            ]
        };

        // 查询指标数据
        async function searchIndicators() {
            toggleLoading(true);
            try {
                // 生成2-5条随机匹配数据
                const randomCount = Math.floor(Math.random() * 4) + 2; // 2-5之间的随机数
                
                // 随机选择本体费用数据并添加随机匹配度
                const randomBentiData = generateRandomData(demoData.benti, randomCount);
                renderBentiTable(randomBentiData);
                
                // 随机选择其他费用数据并添加随机匹配度
                const randomQitaData = generateRandomData(demoData.qita, randomCount);
                renderQitaTable(randomQitaData);
                
                showToast('查询成功', 'success');
            } catch (error) {
                console.error('查询失败:', error);
                showToast('查询失败: ' + error.message, 'error');
            } finally {
                toggleLoading(false);
            }
        }

        // 生成随机匹配数据
        function generateRandomData(sourceData, count) {
            const result = [];
            const usedIndexes = new Set();
            
            while (result.length < count && usedIndexes.size < sourceData.length) {
                const randomIndex = Math.floor(Math.random() * sourceData.length);
                if (!usedIndexes.has(randomIndex)) {
                    usedIndexes.add(randomIndex);
                    const item = { ...sourceData[randomIndex] };
                    // 生成50-90之间的随机匹配度
                    item.matchDegree = Math.floor(Math.random() * 41) + 50;
                    result.push(item);
                }
            }
            
            // 按匹配度降序排序
            return result.sort((a, b) => b.matchDegree - a.matchDegree);
        }

        // 渲染本体费用指标表格
        function renderBentiTable(indicators) {
            const tbody = document.getElementById('bentiTableBody');
            const emptyData = document.getElementById('bentiEmptyData');
            
            if (!indicators || indicators.length === 0) {
                tbody.innerHTML = '';
                emptyData.style.display = 'block';
                return;
            }
            
            emptyData.style.display = 'none';
            tbody.innerHTML = '';
            
            indicators.forEach(indicator => {
                const tr = document.createElement('tr');
                tr.className = selectedBentiIds.includes(indicator.序号) ? 'selected' : '';
                
                // 创建选择列的点击事件
                const checkbox = document.createElement('input');
                checkbox.type = 'checkbox';
                checkbox.checked = selectedBentiIds.includes(indicator.序号);
                checkbox.onclick = (e) => {
                    e.stopPropagation(); // 阻止事件冒泡
                    selectBentiIndicator(indicator.序号, tr);
                };
                
                const td = document.createElement('td');
                td.appendChild(checkbox);
                td.onclick = (e) => {
                    e.stopPropagation(); // 阻止事件冒泡
                    checkbox.checked = !checkbox.checked;
                    selectBentiIndicator(indicator.序号, tr);
                };
                
                tr.appendChild(td);
                tr.innerHTML += `
                    <td><span class="match-tag">${indicator.matchDegree}%</span></td>
                    <td>${indicator['工程名称'] || '-'}</td>
                    <td>${indicator['线路工程'] || '-'}</td>
                    <td data-type="number">${indicator['线路总长度'] || '-'}</td>
                    <td>${indicator['回路数'] || '-'}</td>
                    <td data-type="number">${indicator['风速'] || '-'}</td>
                    <td data-type="number">${indicator['覆冰'] || '-'}</td>
                    <td>${indicator['导线规格'] || '-'}</td>
                    <td data-type="number">${indicator['平地'] || '-'}</td>
                    <td data-type="number">${indicator['丘陵'] || '-'}</td>
                    <td data-type="number">${indicator['山地'] || '-'}</td>
                    <td data-type="number">${indicator['高山'] || '-'}</td>
                    <td data-type="number">${indicator['峻岭'] || '-'}</td>
                    <td data-type="number">${indicator['泥沼'] || '-'}</td>
                    <td data-type="number">${indicator['河网'] || '-'}</td>
                    <td data-type="number">${indicator['人力运距'] || '-'}</td>
                    <td data-type="number">${indicator['汽车运距'] || '-'}</td>
                    <td data-type="number">${indicator['铁塔基数'] || '-'}</td>
                    <td data-type="number">${indicator['直线塔'] || '-'}</td>
                    <td data-type="number">${indicator['耐张塔'] || '-'}</td>
                    <td data-type="number">${indicator['耐张比例'] || '-'}</td>
                    <td data-type="number">${indicator['导线'] || '-'}</td>
                    <td data-type="number">${indicator['塔材'] || '-'}</td>
                    <td data-type="number">${indicator['基础钢材'] || '-'}</td>
                    <td data-type="number">${indicator['地脚螺栓和插入式角钢'] || '-'}</td>
                    <td data-type="number">${indicator['挂线金具'] || '-'}</td>
                    <td data-type="number">${indicator['导线间隔棒'] || '-'}</td>
                    <td data-type="number">${indicator['防振锤'] || '-'}</td>
                    <td data-type="number">${indicator['导线防振锤'] || '-'}</td>
                    <td data-type="number">${indicator['地线防振锤'] || '-'}</td>
                    <td data-type="number">${indicator['合成复合绝缘子'] || '-'}</td>
                    <td data-type="number">${indicator['玻璃绝缘子盘式绝缘子'] || '-'}</td>
                    <td data-type="number">${indicator['硬跳'] || '-'}</td>
                    <td data-type="number">${indicator['现浇混凝土'] || '-'}</td>
                    <td data-type="number">${indicator['灌柱桩基础混凝土'] || '-'}</td>
                    <td data-type="number">${indicator['基础护壁'] || '-'}</td>
                    <td data-type="number">${indicator['基础垫层'] || '-'}</td>
                    <td data-type="number">${indicator['钻孔灌注桩深度'] || '-'}</td>
                    <td data-type="number">${indicator['护坡挡土墙'] || '-'}</td>
                    <td data-type="number">${indicator['土方量'] || '-'}</td>
                    <td data-type="number">${indicator['基坑土方非机械'] || '-'}</td>
                    <td data-type="number">${indicator['基坑土方机械'] || '-'}</td>
                    <td data-type="number">${indicator['接地槽'] || '-'}</td>
                    <td data-type="number">${indicator['排水沟'] || '-'}</td>
                    <td data-type="number">${indicator['尖峰基面'] || '-'}</td>
                    <td data-type="number">${indicator['本体工程'] || '-'}</td>
                    <td data-type="number">${indicator['基础工程'] || '-'}</td>
                    <td data-type="number">${indicator['杆塔工程'] || '-'}</td>
                    <td data-type="number">${indicator['接地工程'] || '-'}</td>
                    <td data-type="number">${indicator['架线工程'] || '-'}</td>
                    <td data-type="number">${indicator['附件工程'] || '-'}</td>
                    <td data-type="number">${indicator['辅助工程'] || '-'}</td>
                `;
                tbody.appendChild(tr);
            });
        }

        // 渲染其他费用指标表格
        function renderQitaTable(indicators) {
            const tbody = document.getElementById('qitaTableBody');
            const emptyData = document.getElementById('qitaEmptyData');
            
            if (!indicators || indicators.length === 0) {
                tbody.innerHTML = '';
                emptyData.style.display = 'block';
                return;
            }
            
            emptyData.style.display = 'none';
            tbody.innerHTML = '';
            
            indicators.forEach(indicator => {
                const tr = document.createElement('tr');
                tr.className = selectedQitaIds.includes(indicator.序号) ? 'selected' : '';
                
                // 创建选择列的点击事件
                const checkbox = document.createElement('input');
                checkbox.type = 'checkbox';
                checkbox.checked = selectedQitaIds.includes(indicator.序号);
                checkbox.onclick = (e) => {
                    e.stopPropagation(); // 阻止事件冒泡
                    selectQitaIndicator(indicator.序号, tr);
                };
                
                const td = document.createElement('td');
                td.appendChild(checkbox);
                td.onclick = (e) => {
                    e.stopPropagation(); // 阻止事件冒泡
                    checkbox.checked = !checkbox.checked;
                    selectQitaIndicator(indicator.序号, tr);
                };
                
                tr.appendChild(td);
                tr.innerHTML += `
                    <td><span class="match-tag">${indicator.matchDegree}%</span></td>
                    <td>${indicator['工程名称'] || '-'}</td>
                    <td>${indicator['线路工程'] || '-'}</td>
                    <td data-type="number">${indicator['合并气象区总长度'] || '-'}</td>
                    <td data-type="number">${indicator['项目建设管理费'] || '-'}</td>
                    <td data-type="number">${indicator['项目法人管理费'] || '-'}</td>
                    <td data-type="number">${indicator['招标费'] || '-'}</td>
                    <td data-type="number">${indicator['工程监理费'] || '-'}</td>
                    <td data-type="number">${indicator['施工过程造价咨询及竣工结算审核费'] || '-'}</td>
                    <td data-type="number">${indicator['工程保险费'] || '-'}</td>
                    <td data-type="number">${indicator['项目建设技术服务费'] || '-'}</td>
                    <td data-type="number">${indicator['项目前期工作费'] || '-'}</td>
                    <td data-type="number">${indicator['勘察设计费'] || '-'}</td>
                    <td data-type="number">${indicator['勘察费'] || '-'}</td>
                    <td data-type="number">${indicator['设计费'] || '-'}</td>
                    <td data-type="number">${indicator['基本设计费'] || '-'}</td>
                    <td data-type="number">${indicator['其他设计费'] || '-'}</td>
                    <td data-type="number">${indicator['设计文件评审费'] || '-'}</td>
                    <td data-type="number">${indicator['可行性研究文件评审费'] || '-'}</td>
                    <td data-type="number">${indicator['初步设计文件评审费'] || '-'}</td>
                    <td data-type="number">${indicator['施工图文件评审费'] || '-'}</td>
                    <td data-type="number">${indicator['工程建设检测费'] || '-'}</td>
                    <td data-type="number">${indicator['电力工程质量检测费'] || '-'}</td>
                    <td data-type="number">${indicator['桩基检测费'] || '-'}</td>
                    <td data-type="number">${indicator['电力工程技术经济标准编制费'] || '-'}</td>
                    <td data-type="number">${indicator['生产准备费'] || '-'}</td>
                    <td data-type="number">${indicator['管理车辆购置费'] || '-'}</td>
                    <td data-type="number">${indicator['工器具及办公家具购置费'] || '-'}</td>
                    <td data-type="number">${indicator['生产职工培训及提前进场费'] || '-'}</td>
                    <td data-type="number">${indicator['专业爆破服务费'] || '-'}</td>
                `;
                tbody.appendChild(tr);
            });
        }

        // 选择本体费用指标
        function selectBentiIndicator(indicatorId, tr) {
            const checkbox = tr.querySelector('input[type="checkbox"]');
            const isChecked = checkbox.checked;
            tr.classList.toggle('selected', isChecked);
            
            if (isChecked) {
                if (!selectedBentiIds.includes(indicatorId)) {
                    selectedBentiIds.push(indicatorId);
                    console.log('添加本体费用指标:', indicatorId, '当前选中:', selectedBentiIds);
                }
            } else {
                selectedBentiIds = selectedBentiIds.filter(id => id !== indicatorId);
                console.log('移除本体费用指标:', indicatorId, '当前选中:', selectedBentiIds);
            }
        }

        // 选择其他费用指标
        function selectQitaIndicator(indicatorId, tr) {
            const checkbox = tr.querySelector('input[type="checkbox"]');
            const isChecked = checkbox.checked;
            tr.classList.toggle('selected', isChecked);
            
            if (isChecked) {
                if (!selectedQitaIds.includes(indicatorId)) {
                    selectedQitaIds.push(indicatorId);
                    console.log('添加其他费用指标:', indicatorId, '当前选中:', selectedQitaIds);
                }
            } else {
                selectedQitaIds = selectedQitaIds.filter(id => id !== indicatorId);
                console.log('移除其他费用指标:', indicatorId, '当前选中:', selectedQitaIds);
            }
        }

        // 切换指标类型标签
        function switchTab(type, section) {
            const buttons = document.querySelectorAll(`.indicator-tabs button`);
            buttons.forEach(btn => {
                if (btn.textContent.includes(type === 'benti' ? '本体' : '其他')) {
                    btn.classList.add('active');
                } else {
                    btn.classList.remove('active');
                }
            });
            
            // 切换面板显示
            document.getElementById(`bentiPanel-${section}`).style.display = type === 'benti' ? 'block' : 'none';
            document.getElementById(`qitaPanel-${section}`).style.display = type === 'benti' ? 'none' : 'block';
        }

        // 创建基准指标
        function createBaseIndicator() {
            console.log('Creating base indicator...'); // 调试日志
            const modalHtml = `
                <div class="modal-backdrop">
                    <div class="modal-dialog">
                        <div class="modal-header">
                            <h3>创建基准指标</h3>
                        </div>
                        <div class="modal-body">
                            <p>是否将所选历史指标数据创建为基准指标，如所选历史指标为多条，将进行加权平均做为基准指标。</p>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" onclick="closeModal()">取消</button>
                            <button type="button" class="btn btn-primary" onclick="confirmModal()">确定</button>
                        </div>
                    </div>
                </div>
            `;
            
            // 移除可能存在的旧模态框
            const oldModal = document.querySelector('.modal-backdrop');
            if (oldModal) {
                oldModal.remove();
            }
            
            // 添加新模态框
            document.body.insertAdjacentHTML('beforeend', modalHtml);
            
            // 添加点击背景关闭模态框的功能
            const modal = document.querySelector('.modal-backdrop');
            modal.addEventListener('click', function(e) {
                if (e.target === this) {
                    closeModal();
                }
            });
        }

        // 关闭模态框
        function closeModal() {
            const modal = document.querySelector('.modal-backdrop');
            if (modal) {
                modal.remove();
            }
        }

        // 确认创建
        function confirmModal() {
            hideModal();
            loadBaseIndicatorData(); // 在确认后加载数据
            showToast('基准指标创建成功');
        }

        function loadBaseIndicatorData() {
            // 加载本体费用指标演示数据
            const bentiTbody = document.getElementById('bentiBaseTableBody');
            if (bentiTbody) {
                bentiTbody.innerHTML = `
                    <tr>
                        <td>2.65</td>
                        <td>2.12</td>
                        <td>0.53</td>
                        <td>20</td>
                        <td>2.98</td>
                        <td>15.89</td>
                        <td>1.19</td>
                        <td>0.40</td>
                        <td>0.50</td>
                        <td>5.30</td>
                        <td>10.60</td>
                        <td>7.95</td>
                        <td>2.65</td>
                        <td>15.90</td>
                        <td>0</td>
                        <td>2.65</td>
                        <td>79.44</td>
                        <td>26.48</td>
                        <td>3.97</td>
                        <td>2.65</td>
                        <td>39.72</td>
                        <td>5.30</td>
                        <td>264.85</td>
                        <td>79.46</td>
                        <td>185.40</td>
                        <td>7.94</td>
                        <td>5.30</td>
                        <td>2.65</td>
                        <td>165.56</td>
                        <td>49.67</td>
                        <td>66.21</td>
                        <td>8.28</td>
                        <td>33.11</td>
                        <td>4.97</td>
                        <td>3.31</td>
                        <td>500</td>
                    </tr>
                `;
            }

            // 加载其他费用指标演示数据
            const qitaTbody = document.getElementById('qitaBaseTableBody');
            if (qitaTbody) {
                qitaTbody.innerHTML = `
                    <tr>
                        <td>7.95</td>
                        <td>3.97</td>
                        <td>1.59</td>
                        <td>3.18</td>
                        <td>0.79</td>
                        <td>2.38</td>
                        <td>4.77</td>
                        <td>6.36</td>
                        <td>9.54</td>
                        <td>3.18</td>
                        <td>6.36</td>
                        <td>4.77</td>
                        <td>1.59</td>
                        <td>0.79</td>
                        <td>0.32</td>
                        <td>0.23</td>
                        <td>0.23</td>
                        <td>1.59</td>
                        <td>1.19</td>
                        <td>0.40</td>
                        <td>0.79</td>
                        <td>3.18</td>
                        <td>1.59</td>
                        <td>0.79</td>
                        <td>0.64</td>
                        <td>0.16</td>
                        <td>1000</td>
                    </tr>
                `;
            }
        }

        // 编辑基准指标
        function editBaseIndicator() {
            const editButton = document.querySelector('.btn-edit');
            const bentiBaseCells = document.querySelectorAll('#bentiBaseTableBody tr:first-child td:not(:first-child)');
            const qitaBaseCells = document.querySelectorAll('#qitaBaseTableBody tr:first-child td:not(:first-child)');
            
            // 如果当前是编辑模式，则保存
            if (editButton.textContent === '保存') {
                // 移除可编辑状态
                bentiBaseCells.forEach(cell => {
                    cell.contentEditable = 'false';
                    cell.classList.remove('editable-cell');
                });
                
                qitaBaseCells.forEach(cell => {
                    cell.contentEditable = 'false';
                    cell.classList.remove('editable-cell');
                });
                
                // 更改按钮文本
                editButton.textContent = '基准指标编辑';
                
                // 显示保存成功提示
                showToast('基准指标保存成功', 'success');
            } else {
                // 使单元格可编辑
                bentiBaseCells.forEach(cell => {
                    cell.contentEditable = 'true';
                    cell.classList.add('editable-cell');
                    
                    // 如果单元格为空，添加随机数据
                    if (!cell.textContent.trim()) {
                        cell.textContent = (Math.random() * 10).toFixed(2);
                    }
                });
                
                qitaBaseCells.forEach(cell => {
                    cell.contentEditable = 'true';
                    cell.classList.add('editable-cell');
                    
                    // 如果单元格为空，添加随机数据
                    if (!cell.textContent.trim()) {
                        cell.textContent = (Math.random() * 10).toFixed(2);
                    }
                });
                
                // 更改按钮文本
                editButton.textContent = '保存';
            }
        }
        
        // 单价管理功能
        function managePrice() {
            // 显示单价管理模态框
            document.getElementById('priceManagerModal').style.display = 'block';
            
            // 加载单价数据
            loadPriceData();
        }
        
        // 关闭单价管理模态框
        function closePriceModal() {
            document.getElementById('priceManagerModal').style.display = 'none';
        }
        
        // 保存单价设置
        function savePriceSettings() {
            // 获取所有单价输入框的值
            const bentiPrices = document.querySelectorAll('#bentiPriceTableBody tr input');
            const qitaPrices = document.querySelectorAll('#qitaPriceTableBody tr input');
            
            // 更新单价数据
            bentiPrices.forEach((input, index) => {
                bentiPriceData[index].price = parseFloat(input.value) || 0;
            });
            
            qitaPrices.forEach((input, index) => {
                qitaPriceData[index].price = parseFloat(input.value) || 0;
            });
            
            // 更新基准指标表格中的单价列
            const bentiBaseRow = document.querySelector('#bentiBaseTableBody tr');
            const qitaBaseRow = document.querySelector('#qitaBaseTableBody tr');
            
            if (bentiBaseRow) {
                const cells = bentiBaseRow.getElementsByTagName('td');
                if (cells.length > 0) {
                    // 更新单价列（最后一列）
                    const avgPrice = bentiPriceData.reduce((sum, item) => sum + item.price, 0) / bentiPriceData.length;
                    cells[cells.length - 1].textContent = avgPrice.toFixed(0);
                }
            }
            
            if (qitaBaseRow) {
                const cells = qitaBaseRow.getElementsByTagName('td');
                if (cells.length > 0) {
                    // 更新单价列（最后一列）
                    const avgPrice = qitaPriceData.reduce((sum, item) => sum + item.price, 0) / qitaPriceData.length;
                    cells[cells.length - 1].textContent = avgPrice.toFixed(0);
                }
            }
            
            // 更新指标单价行的数据
            updateUnitPriceRow('bentiTotalTableBody', bentiPriceData);
            updateUnitPriceRow('qitaTotalTableBody', qitaPriceData);
            
            // 关闭模态框
            closePriceModal();
            
            // 显示保存成功提示
            showToast('单价设置保存成功', 'success');
        }
        
        // 更新指标单价行数据
        function updateUnitPriceRow(tableId, priceData) {
            const tbody = document.getElementById(tableId);
            if (!tbody) return;
            
            const row = tbody.rows[0]; // 第一行是指标单价行
            if (!row) return;
            
            // 跳过第一列（价格列），填充指标数据
            for (let i = 0; i < priceData.length; i++) {
                if (row.cells[i + 1]) {
                    row.cells[i + 1].textContent = (Math.random() * 10).toFixed(2); // 随机数据，可根据需要修改
                }
            }
            
            // 最后一列填充平均单价
            if (row.cells[row.cells.length - 1]) {
                const avgPrice = priceData.reduce((sum, item) => sum + item.price, 0) / priceData.length;
                row.cells[row.cells.length - 1].textContent = avgPrice.toFixed(0);
            }
        }
        
        // 组价计算函数
        function calculatePrice() {
            // 提示用户正在计算
            showToast('正在计算单公里量乘以指标单价，得出单公里组价结果', 'info');
            
            // 计算本体费用指标组价
            calculateGroupPrice('bentiBaseTableBody');
            
            // 计算其他费用指标组价
            calculateGroupPrice('qitaBaseTableBody');
            
            // 增加组价相关提示
            showToast('组价计算完成', 'success');
        }
        
        // 计算组价（第四行数据 = 第一行数据 * 第三行数据）
        function calculateGroupPrice(tableId) {
            const tbody = document.getElementById(tableId);
            if (!tbody) return;
            
            const row1 = tbody.rows[0]; // 第一行（单公里量）
            const row3 = tbody.rows[2]; // 第三行（指标单价）
            const row4 = tbody.rows[3]; // 第四行（单公里组价）
            
            if (!row1 || !row3 || !row4) return;
            
            // 跳过第一列（价格列），计算其他列
            for (let i = 1; i < row3.cells.length; i++) {
                const value1 = parseFloat(row1.cells[i].textContent) || 0;
                const value3 = parseFloat(row3.cells[i].textContent) || 0;
                row4.cells[i].textContent = (value1 * value3).toFixed(2);
            }
        }

        // 保存指标选择
        async function saveIndicatorSelection() {
            try {
                // 获取当前特征段数据
                const sectionData = {
                    特征段名称: document.getElementById('sectionName').textContent,
                    线路长度: document.getElementById('lineLength').textContent,
                    风速: document.getElementById('windSpeed').textContent,
                    覆冰: document.getElementById('iceThickness').textContent,
                    回路数: document.getElementById('circuitCount').textContent,
                    导线规格: document.getElementById('wireSpec').textContent,
                    组价计算状态: '已组价',  // 更新状态为已组价
                    指标选择状态: '已选择'   // 添加指标选择状态
                };

                // 调用父窗口的更新函数
                window.parent.updateSectionStatus(sectionData);
                
                // 关闭模态框
                window.parent.closeIndicatorSelectModal();
                
                showToast('保存成功', 'success');
            } catch (error) {
                console.error('保存失败:', error);
                showToast('保存失败', 'error');
            }
        }

        function showModal() {
            console.log('显示确认对话框');
            const confirmModal = document.getElementById('confirmModal');
            if (confirmModal) {
                confirmModal.style.display = 'block';
            } else {
                console.error('找不到确认对话框元素');
                alert('确认对话框元素不存在，请检查HTML结构');
            }
        }

        function hideModal() {
            console.log('隐藏确认对话框');
            const confirmModal = document.getElementById('confirmModal');
            if (confirmModal) {
                confirmModal.style.display = 'none';
            } else {
                console.error('找不到确认对话框元素');
            }
        }

        document.addEventListener('DOMContentLoaded', function() {
            // 绑定创建基准指标按钮点击事件
            const createBaseBtn = document.querySelector('.tab-header button');
            if (createBaseBtn) {
                createBaseBtn.addEventListener('click', createBaseIndicator);
            }
        });

        // 页面加载时填充特征段信息
        document.addEventListener('DOMContentLoaded', function() {
            // 从 sessionStorage 获取特征段数据
            const sectionDataStr = sessionStorage.getItem('selectedSectionData');
            if (sectionDataStr) {
                const sectionData = JSON.parse(sectionDataStr);
                
                // 填充特征段信息
                document.getElementById('sectionName').textContent = sectionData.特征段名称 || '-';
                document.getElementById('lineLength').textContent = sectionData.线路长度 || '-';
                document.getElementById('windSpeed').textContent = sectionData.风速 || '-';
                document.getElementById('iceThickness').textContent = sectionData.覆冰 || '-';
                document.getElementById('circuitCount').textContent = sectionData.回路数 || '-';
                document.getElementById('wireSpec').textContent = sectionData.导线规格 || '-';
                
                // 清除 sessionStorage 中的数据
                sessionStorage.removeItem('selectedSectionData');
            }
            
            // 初始化表格，显示两行空数据
            initTotalTables();
            initBaseTables();

            // 添加事件监听器
            document.getElementById('saveHistoryBtn').addEventListener('click', function(e) {
                e.preventDefault();
                console.log('保存按钮被点击');
                confirmCreateBaseIndicator();
            });
        });
        
        // 初始化表格，显示两行空数据
        function initTotalTables() {
            // 初始化本体费用指标表格
            initEmptyTable('bentiTotalTableBody', 37); // 37列数据（包括价格列和单价列）
            
            // 初始化其他费用指标表格
            initEmptyTable('qitaTotalTableBody', 28); // 28列数据（包括价格列和单价列）
        }
        
        // 初始化基准指标表格，显示四行空数据
        function initBaseTables() {
            // 初始化本体费用基准指标表格
            initEmptyBaseTable('bentiBaseTableBody', 37); // 37列数据（包括数量列和单价列）
            
            // 初始化其他费用基准指标表格
            initEmptyBaseTable('qitaBaseTableBody', 28); // 28列数据（包括数量列和单价列）
        }
        
        // 初始化空表格
        function initEmptyTable(tableId, columnCount) {
            const tbody = document.getElementById(tableId);
            if (!tbody) return;
            
            // 创建第一行 - 指标单价
            const row1 = document.createElement('tr');
            row1.innerHTML = `<td>指标单价</td>${'<td></td>'.repeat(columnCount - 1)}`;
            
            // 创建第二行 - 单公里组价
            const row2 = document.createElement('tr');
            row2.innerHTML = `<td>单公里组价</td>${'<td></td>'.repeat(columnCount - 1)}`;
            row2.style.whiteSpace = 'nowrap';
            
            // 添加到表格
            tbody.innerHTML = '';
            tbody.appendChild(row1);
            tbody.appendChild(row2);
        }
        
        // 初始化基准指标空表格
        function initEmptyBaseTable(tableId, columnCount) {
            const tbody = document.getElementById(tableId);
            if (!tbody) return;
            
            // 创建第一行 - 单公里量
            const row1 = document.createElement('tr');
            row1.innerHTML = `<td>单公里量</td>${'<td></td>'.repeat(columnCount - 1)}`;
            
            // 创建第二行 - 指标总量
            const row2 = document.createElement('tr');
            row2.innerHTML = `<td>指标总量</td>${'<td></td>'.repeat(columnCount - 1)}`;
            
            // 创建第三行 - 指标单价
            const row3 = document.createElement('tr');
            row3.innerHTML = `<td>指标单价</td>${'<td></td>'.repeat(columnCount - 1)}`;
            
            // 创建第四行 - 单公里组价
            const row4 = document.createElement('tr');
            row4.innerHTML = `<td>单公里组价</td>${'<td></td>'.repeat(columnCount - 1)}`;
            row4.style.whiteSpace = 'nowrap';
            
            // 添加到表格
            tbody.innerHTML = '';
            tbody.appendChild(row1);
            tbody.appendChild(row2);
            tbody.appendChild(row3);
            tbody.appendChild(row4);
        }
        
        // 计算基准指标总量
        function calculateBaseTotal() {
            // 提示用户正在计算
            showToast('正在计算特征段总量 = 单公里量 × 30', 'info');
            
            // 计算本体费用指标总量
            calculateBaseGroupTotal('bentiBaseTableBody');
            
            // 计算其他费用指标总量
            calculateBaseGroupTotal('qitaBaseTableBody');
            
            // 增加计算完成提示
            showToast('总量计算完成', 'success');
        }
        
        // 计算基准指标组总量（第二行数据 = 第一行数据 * 30）
        function calculateBaseGroupTotal(tableId) {
            const tbody = document.getElementById(tableId);
            if (!tbody) return;
            
            const row1 = tbody.rows[0]; // 第一行（单公里量）
            const row2 = tbody.rows[1]; // 第二行（指标总量）
            
            if (!row1 || !row2) return;
            
            // 跳过第一列（数量列），计算其他列
            for (let i = 1; i < row1.cells.length; i++) {
                const value1 = parseFloat(row1.cells[i].textContent) || 0;
                row2.cells[i].textContent = (value1 * 30).toFixed(2);
            }
        }

        // 本体费用指标单价数据
        const bentiPriceData = [
            { name: "铁塔基数", unit: "基", price: 10000 },
            { name: "直线塔", unit: "基", price: 8000 },
            { name: "耐张塔", unit: "基", price: 12000 },
            { name: "耐张比例", unit: "%", price: 500 },
            { name: "导线", unit: "t", price: 15000 },
            { name: "塔材", unit: "t", price: 7000 },
            { name: "基础钢材", unit: "t", price: 6000 },
            { name: "地脚螺栓和插入式角钢", unit: "t", price: 8000 },
            { name: "挂线金具", unit: "t", price: 12000 },
            { name: "导线间隔棒", unit: "套", price: 200 },
            { name: "防振锤", unit: "个", price: 150 },
            { name: "导线防振锤", unit: "个", price: 160 },
            { name: "地线防振锤", unit: "个", price: 140 },
            { name: "合成复合绝缘子", unit: "支", price: 250 },
            { name: "玻璃绝缘子/盘式绝缘子", unit: "支", price: 180 },
            { name: "硬跳", unit: "套", price: 300 },
            { name: "现浇混凝土", unit: "m³", price: 500 },
            { name: "灌柱桩基础混凝土", unit: "m³", price: 600 },
            { name: "基础护壁", unit: "m³", price: 450 },
            { name: "基础垫层", unit: "m³", price: 400 },
            { name: "钻孔灌注桩深度", unit: "m", price: 300 },
            { name: "护坡、挡土墙", unit: "m³", price: 550 },
            { name: "土方量", unit: "m³", price: 100 },
            { name: "基坑土方（非机械）", unit: "m³", price: 150 },
            { name: "基坑土方（机械）", unit: "m³", price: 120 },
            { name: "接地槽", unit: "m³", price: 180 },
            { name: "排水沟", unit: "m³", price: 200 },
            { name: "尖峰、基面", unit: "m³", price: 250 },
            { name: "本体工程", unit: "万元", price: 10000 },
            { name: "基础工程", unit: "万元", price: 10000 },
            { name: "杆塔工程", unit: "万元", price: 10000 },
            { name: "接地工程", unit: "万元", price: 10000 },
            { name: "架线工程", unit: "万元", price: 10000 },
            { name: "附件工程", unit: "万元", price: 10000 },
            { name: "辅助工程", unit: "万元", price: 10000 }
        ];
        
        // 其他费用指标单价数据
        const qitaPriceData = [
            { name: "项目建设管理费", unit: "万元", price: 10000 },
            { name: "项目法人管理费", unit: "万元", price: 10000 },
            { name: "招标费", unit: "万元", price: 10000 },
            { name: "工程监理费", unit: "万元", price: 10000 },
            { name: "施工过程造价咨询及竣工结算审核费", unit: "万元", price: 10000 },
            { name: "工程保险费", unit: "万元", price: 10000 },
            { name: "项目建设技术服务费", unit: "万元", price: 10000 },
            { name: "项目前期工作费", unit: "万元", price: 10000 },
            { name: "勘察设计费", unit: "万元", price: 10000 },
            { name: "勘察费", unit: "万元", price: 10000 },
            { name: "设计费", unit: "万元", price: 10000 },
            { name: "基本设计费", unit: "万元", price: 10000 },
            { name: "其他设计费", unit: "万元", price: 10000 },
            { name: "设计文件评审费", unit: "万元", price: 10000 },
            { name: "可行性研究文件评审费", unit: "万元", price: 10000 },
            { name: "初步设计文件评审费", unit: "万元", price: 10000 },
            { name: "施工图文件评审费", unit: "万元", price: 10000 },
            { name: "工程建设检测费", unit: "万元", price: 10000 },
            { name: "电力工程质量检测费", unit: "万元", price: 10000 },
            { name: "桩基检测费", unit: "万元", price: 10000 },
            { name: "电力工程技术经济标准编制费", unit: "万元", price: 10000 },
            { name: "生产准备费", unit: "万元", price: 10000 },
            { name: "管理车辆购置费", unit: "万元", price: 10000 },
            { name: "工器具及办公家具购置费", unit: "万元", price: 10000 },
            { name: "生产职工培训及提前进场费", unit: "万元", price: 10000 },
            { name: "专业爆破服务费", unit: "万元", price: 10000 }
        ];
        
        // 加载单价数据到表格
        function loadPriceData() {
            const bentiTbody = document.getElementById('bentiPriceTableBody');
            const qitaTbody = document.getElementById('qitaPriceTableBody');
            
            // 清空表格
            bentiTbody.innerHTML = '';
            qitaTbody.innerHTML = '';
            
            // 加载本体费用指标单价
            bentiPriceData.forEach(item => {
                const tr = document.createElement('tr');
                tr.innerHTML = `
                    <td>${item.name}</td>
                    <td>${item.unit}</td>
                    <td><input type="number" class="price-input" value="${item.price}" min="0" step="0.01"></td>
                `;
                bentiTbody.appendChild(tr);
            });
            
            // 加载其他费用指标单价
            qitaPriceData.forEach(item => {
                const tr = document.createElement('tr');
                tr.innerHTML = `
                    <td>${item.name}</td>
                    <td>${item.unit}</td>
                    <td><input type="number" class="price-input" value="${item.price}" min="0" step="0.01"></td>
                `;
                qitaTbody.appendChild(tr);
            });
        }
        
        // 计算组价（第四行数据 = 第一行数据 * 第三行数据）
        function calculateGroupPrice(tableId) {
            const tbody = document.getElementById(tableId);
            if (!tbody) return;
            
            const row1 = tbody.rows[0]; // 第一行（单公里量）
            const row3 = tbody.rows[2]; // 第三行（指标单价）
            const row4 = tbody.rows[3]; // 第四行（单公里组价）
            
            if (!row1 || !row3 || !row4) return;
            
            // 跳过第一列（价格列），计算其他列
            for (let i = 1; i < row3.cells.length; i++) {
                const value1 = parseFloat(row1.cells[i].textContent) || 0;
                const value3 = parseFloat(row3.cells[i].textContent) || 0;
                row4.cells[i].textContent = (value1 * value3).toFixed(2);
            }
        }

        // 显示历史指标查询弹出层
        function showHistoryIndicatorModal() {
            console.log('显示历史指标查询弹出层');
            document.getElementById('historyIndicatorModal').style.display = 'block';
            
            // 清空已选择的指标
            selectedBentiIds = [];
            selectedQitaIds = [];
            
            // 清空表格数据
            document.getElementById('bentiTableBody').innerHTML = '';
            document.getElementById('qitaTableBody').innerHTML = '';
            
            // 显示空数据提示
            document.getElementById('bentiEmptyData').style.display = 'block';
            document.getElementById('qitaEmptyData').style.display = 'block';
            
        }
        
        // 隐藏历史指标查询弹出层
        function hideHistoryIndicatorModal() {
            console.log('隐藏历史指标查询弹出层');
            document.getElementById('historyIndicatorModal').style.display = 'none';
        }
        
        // 确认创建基准指标
        function confirmCreateBaseIndicator() {
            console.log('确认创建基准指标函数被调用');
            console.log('selectedBentiIds:', selectedBentiIds);
            console.log('selectedQitaIds:', selectedQitaIds);
            
            // 模拟选中指标，用于测试
            if (selectedBentiIds.length === 0 && selectedQitaIds.length === 0) {
                console.log('没有选中任何指标，添加测试数据');
                selectedBentiIds = [1, 2]; // 添加测试数据
                console.log('添加测试数据后 selectedBentiIds:', selectedBentiIds);
            }
            
            // 如果没有选择任何指标，显示错误提示
            if (selectedBentiIds.length === 0 && selectedQitaIds.length === 0) {
                showToast('请至少选择一条历史指标数据', 'error');
                return;
            }
            
            // 显示确认对话框
            showModal();
        }
        
        // 确认模态框操作
        function confirmModal() {
            console.log('确认模态框操作');
            hideModal();
            
            // 隐藏历史指标查询弹出层
            hideHistoryIndicatorModal();
            
            // 生成加权平均的基准指标数据
            generateWeightedAverageBaseIndicatorData();
            
            // 显示提示信息
            showToast('基准指标创建成功', 'success');
        }
        
        // 生成加权平均的基准指标数据
        function generateWeightedAverageBaseIndicatorData() {
            // 获取本体费用基准指标表格的第一行（单公里量）
            const bentiBaseRow = document.querySelector('#bentiBaseTableBody tr:first-child');
            if (bentiBaseRow) {
                // 跳过第一列（数量列），填充随机数据，模拟加权平均结果
                for (let i = 1; i < bentiBaseRow.cells.length; i++) {
                    if (i < bentiBaseRow.cells.length - 1) { // 最后一列是单价，不填充
                        // 生成2-8之间的随机数，模拟加权平均结果
                        const weightedValue = (Math.random() * 6 + 2).toFixed(2);
                        bentiBaseRow.cells[i].textContent = weightedValue;
                    } else {
                        // 最后一列是单价
                        bentiBaseRow.cells[i].textContent = '500';
                    }
                }
            }
            
            // 获取其他费用基准指标表格的第一行（单公里量）
            const qitaBaseRow = document.querySelector('#qitaBaseTableBody tr:first-child');
            if (qitaBaseRow) {
                // 跳过第一列（数量列），填充随机数据，模拟加权平均结果
                for (let i = 1; i < qitaBaseRow.cells.length; i++) {
                    if (i < qitaBaseRow.cells.length - 1) { // 最后一列是单价，不填充
                        // 生成2-8之间的随机数，模拟加权平均结果
                        const weightedValue = (Math.random() * 6 + 2).toFixed(2);
                        qitaBaseRow.cells[i].textContent = weightedValue;
                    } else {
                        // 最后一列是单价
                        qitaBaseRow.cells[i].textContent = '1000';
                    }
                }
            }
            
            // 清空其他行的数据
            clearOtherRowsData('bentiBaseTableBody');
            clearOtherRowsData('qitaBaseTableBody');
        }
        
        // 清空除第一行外的其他行数据
        function clearOtherRowsData(tableId) {
            const tbody = document.getElementById(tableId);
            if (!tbody) return;
            
            for (let rowIndex = 1; rowIndex < tbody.rows.length; rowIndex++) {
                const row = tbody.rows[rowIndex];
                for (let i = 1; i < row.cells.length; i++) {
                    row.cells[i].textContent = '';
                }
            }
        }
        
        // 保存单价设置
        function savePriceSettings() {
            // 获取所有单价输入框的值
            const bentiPrices = document.querySelectorAll('#bentiPriceTableBody tr input');
            const qitaPrices = document.querySelectorAll('#qitaPriceTableBody tr input');
            
            // 更新单价数据
            bentiPrices.forEach((input, index) => {
                bentiPriceData[index].price = parseFloat(input.value) || 0;
            });
            
            qitaPrices.forEach((input, index) => {
                qitaPriceData[index].price = parseFloat(input.value) || 0;
            });
            
            // 更新基准指标表格中的单价列
            updateBaseTablePriceColumn('bentiBaseTableBody', bentiPriceData);
            updateBaseTablePriceColumn('qitaBaseTableBody', qitaPriceData);
            
            // 更新指标单价行的数据
            updateUnitPriceRow('bentiBaseTableBody', bentiPriceData);
            updateUnitPriceRow('qitaTotalTableBody', qitaPriceData);
            
            // 关闭模态框
            closePriceModal();
            
            // 显示保存成功提示
            showToast('单价设置保存成功', 'success');
        }
        
        // 更新基准指标表格中的单价列
        function updateBaseTablePriceColumn(tableId, priceData) {
            const tbody = document.getElementById(tableId);
            if (!tbody) return;
            
            const rows = tbody.rows;
            if (rows.length > 0) {
                // 更新每一行的最后一列（单价列）
                for (let i = 0; i < rows.length; i++) {
                    const cells = rows[i].cells;
                    if (cells.length > 0) {
                        // 更新单价列
                        const avgPrice = priceData.reduce((sum, item) => sum + item.price, 0) / priceData.length;
                        cells[cells.length - 1].textContent = avgPrice.toFixed(0);
                    }
                }
            }
        }
        
        // 更新指标单价行数据
        function updateUnitPriceRow(tableId, priceData) {
            const tbody = document.getElementById(tableId);
            if (!tbody) return;
            
            const row = tbody.rows[2]; // 第三行是指标单价行
            if (!row) return;
            
            // 跳过第一列（价格列），填充指标数据
            for (let i = 0; i < priceData.length; i++) {
                if (row.cells[i + 1]) {
                    row.cells[i + 1].textContent = (Math.random() * 10).toFixed(2); // 随机数据，可根据需要修改
                }
            }
            
            // 最后一列填充平均单价
            if (row.cells[row.cells.length - 1]) {
                const avgPrice = priceData.reduce((sum, item) => sum + item.price, 0) / priceData.length;
                row.cells[row.cells.length - 1].textContent = avgPrice.toFixed(0);
            }
        }
</script> 
</body>
</html> 