{% extends "base.html" %}

{% block head %}
{{ super() }}
<link rel="stylesheet" href="{{ url_for('static', filename='css/pricing.css') }}">
{% endblock %}

{% block content %}
<div class="workspace-container">
    <div class="workspace-content panels-collapsed">
        <!-- 左侧工程管理面板 -->
        <div class="project-panel">
            {% include "quick_pricing/project_management.html" %}
        </div>

        <!-- 右侧面板容器 -->
        <div class="right-panels">
            <!-- 上方特征段管理面板 -->
            <div class="section-panel">
                {% include "quick_pricing/section_management.html" %}
            </div>

            {# 下方指标选择面板 - 暂时屏蔽
            <div class="indicator-panel">
                {% include "quick_pricing/section_pricingSum.html" %}
            </div>
            #}
        </div>
    </div>
</div>

<style>
/* 工作区容器 */
.workspace-container {
    position: fixed;
    top: 56px; /* 导航栏高度 */
    left: 0;
    right: 0;
    bottom: 0;
    overflow: hidden;
    background-color: #f0f2f5;
    padding: 0.5rem;
}

.workspace-content {
    display: flex;
    width: 100%;
    height: 100%;
    gap: 0.5rem;
    transition: all 0.3s ease-in-out; /* 添加过渡效果 */
}

/* 面板折叠状态 */
.panels-collapsed .project-panel {
    width: 100%; /* 工程管理面板占满宽度 */
    transition: width 0.3s ease-in-out;
}

.panels-collapsed .right-panels {
    width: 0%; /* 特征段管理面板宽度为0 */
    overflow: hidden;
    transition: width 0.3s ease-in-out;
}

/* 面板展开状态 */
.panels-expanded .project-panel {
    width: 45%; /* 工程管理面板宽度 */
    transition: width 0.3s ease-in-out;
}

.panels-expanded .right-panels {
    width: 55%; /* 特征段管理面板宽度 */
    transition: width 0.3s ease-in-out;
}

/* 左侧工程管理面板 */
.project-panel {
    height: 100%;
    border: 1px solid #e8e8e8;
    border-radius: 2px;
    background-color: #fff;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
    overflow: auto;
}

/* 右侧面板容器 */
.right-panels {
    height: 100%;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

/* 特征段管理面板 */
.section-panel {
    height: 100%; /* 修改为100%占满整个右侧空间 */
    border: 1px solid #e8e8e8;
    border-radius: 2px;
    background-color: #fff;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
    overflow: auto;
}

/* {# 指标选择面板样式 - 暂时屏蔽
.indicator-panel {
    height: 50%;
    border: 1px solid #e8e8e8;
    border-radius: 2px;
    background-color: #fff;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
    overflow: auto;
}
#} */

/* 定义全局变量 */
:root {
    --primary-color: #0071e3;
    --primary-hover: #0077ed;
    --secondary-color: #86868b;
    --text-color: #1d1d1f;
    --light-gray: #f5f5f7;
    --border-color: #d2d2d7;
    --success-color: #34c759;
    --warning-color: #ff9500;
    --error-color: #ff3b30;
    --box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

/* 全局表单控件样式 */
.workspace-container select,
.workspace-container select option {
    font-size: 12px !important;
}

/* Toast提示样式 */
.toast {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 24px;
    border-radius: 4px;
    z-index: 1000;
    animation: slideIn 0.3s ease-out;
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16);
    max-width: 300px;
    word-wrap: break-word;
}

.toast.success {
    background-color: #f6ffed;
    border: 1px solid #b7eb8f;
    color: #52c41a;
}

.toast.error {
    background-color: #fff2f0;
    border: 1px solid #ffccc7;
    color: #ff4d4f;
}

.toast.info {
    background-color: #e6f7ff;
    border: 1px solid #91d5ff;
    color: #1890ff;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* ------------- 取消按钮悬停变色（Quick Pricing） ------------- */
.workspace-container .btn-primary:hover,
.workspace-container .btn-secondary:hover,
.workspace-container .btn-info:hover,
.workspace-container .btn-danger:hover {
    background-color: inherit !important;
    border-color: inherit !important;
    color: inherit !important;
    transform: none !important;
}
</style>

<!-- 引入相关的JavaScript文件 -->
<script src="{{ url_for('static', filename='js/project_management.js') }}"></script>
<script src="{{ url_for('static', filename='js/section_management.js') }}"></script>
{# 暂时屏蔽指标选择的JS文件
<script src="{{ url_for('static', filename='js/section_pricingSum.js') }}"></script>
#}

<script>
// 工作区管理对象
const workspaceManager = {
    // 初始化工作区
    init: function() {
        this.loadWorkspaceData();
        this.bindEvents();
        this.initPanelState();
    },

    // 初始化面板状态
    initPanelState: function() {
        const workspaceContent = document.querySelector('.workspace-content');
        if (workspaceContent) {
            workspaceContent.classList.add('panels-collapsed');
            workspaceContent.classList.remove('panels-expanded');
        }
    },

    // 加载工作区数据
    loadWorkspaceData: function() {
        // 加载工程列表
        if (typeof loadProjectData === 'function') {
            loadProjectData();
        }
    },

    // 刷新工作区
    refreshWorkspace: function() {
        this.loadWorkspaceData();
    },

    // 绑定事件
    bindEvents: function() {
        // 监听工程创建成功事件
        document.addEventListener('projectCreated', () => {
            this.refreshWorkspace();
        });

        // 监听工程删除成功事件
        document.addEventListener('projectDeleted', () => {
            this.refreshWorkspace();
            // 重置面板状态
            if (typeof resetPanelState === 'function') {
                resetPanelState();
            }
        });
    }
};

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    workspaceManager.init();
});

// 导出全局刷新方法
window.refreshWorkspace = function() {
    workspaceManager.refreshWorkspace();
};
</script>
{% endblock %} 