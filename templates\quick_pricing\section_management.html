<!-- 特征段管理 -->
<section class="pricing-sm-section">
    <!-- 导航栏 -->
    <div class="pricing-sm-panel-nav">
        <div class="pricing-sm-nav-left">
            <h2 class="pricing-sm-nav-title">特征段管理</h2>
            <div class="pricing-sm-nav-breadcrumb">
                <span>快速组价</span>
                <i class="pricing-sm-nav-separator">/</i>
                <span>工程管理</span>
                <i class="pricing-sm-nav-separator">/</i>
                <span class="current">特征段管理</span>
            </div>
        </div>
        <div class="pricing-sm-nav-right">
            <span class="pricing-sm-nav-stats">共 <span id="sectionCount">0</span> 个特征段</span>
            <button class="pricing-shared-btn pricing-shared-btn-primary" onclick="showCreateSectionModal()">新建特征段</button>
            <button class="pricing-shared-btn pricing-shared-btn-secondary" onclick="backToProjects()">
                <i class="fas fa-arrow-left"></i> 回到工程管理
            </button>
        </div>
    </div>

    <!-- 添加工程信息展示区域 -->
    <div class="pricing-sm-project-info">
        <div class="pricing-sm-info-item">
            <label>工程名称:</label>
            <span id="projectName" class="pricing-sm-info-value">500kV东莞西南部受电通道工程</span>
        </div>
        <div class="pricing-sm-info-item">
            <label>电压等级:</label>
            <span id="voltageLevel" class="pricing-sm-info-value">500kV</span>
        </div>
        <div class="pricing-sm-info-item">
            <label>线路总长度(km):</label>
            <span id="totalLineLength" class="pricing-sm-info-value">25</span>
        </div>
    </div>

    <!-- 查询条件区域 -->
    <div class="pricing-sm-search-area" style="display: none;">
        <div class="pricing-sm-search-form">
            <div class="pricing-sm-search-item">
                <label>线路总长度(km)</label>
                <input type="number" id="lineLength" class="pricing-shared-input" placeholder="请输入线路长度">
            </div>
            <div class="pricing-sm-search-item">
                <label>风速(m/s)</label>
                <input type="number" id="windSpeed" class="pricing-shared-input" placeholder="请输入风速">
            </div>
            <div class="pricing-sm-search-item">
                <label>覆冰(mm)</label>
                <input type="number" id="iceThickness" class="pricing-shared-input" placeholder="请输入覆冰厚度">
            </div>
            <div class="pricing-sm-search-item">
                <label>回路数</label>
                <select id="circuitCountSearch" class="pricing-shared-select">
                    <option value="">全部</option>
                    <option value="单回路">单回路</option>
                    <option value="双回路">双回路</option>
                </select>
            </div>
            <div class="pricing-sm-search-item">
                <label>导线规格</label>
                <input type="text" id="wireSpecSearch" class="pricing-shared-input" placeholder="请输入导线规格">
            </div>
        </div>
        <div class="pricing-sm-search-actions">
            <button class="pricing-shared-btn pricing-shared-btn-secondary" onclick="resetSearch()">重置</button>
            <button class="pricing-shared-btn pricing-shared-btn-primary" onclick="searchSections()">查询</button>
        </div>
    </div>

    <!-- 特征段列表 -->
    <div class="pricing-sm-content-wrapper">
        <div class="pricing-sm-table-container" style="overflow-x: auto;">
            <table class="pricing-sm-table">
                <thead>
                    <tr>
                        <th>特征段名称</th>
                        <th>线路长度(km)</th>
                        <th>风速(m/s)</th>
                        <th>覆冰(mm)</th>
                        <th>回路数</th>
                        <th>导线规格</th>
                        <th>组价计算状态</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="sectionTableBody">
                    <!-- 特征段数据将通过JavaScript动态加载 -->
                    <template id="sectionRowTemplate">
                        <tr>
                            <td data-field="特征段名称"></td>
                            <td data-field="线路长度"></td>
                            <td data-field="风速"></td>
                            <td data-field="覆冰"></td>
                            <td data-field="回路数"></td>
                            <td data-field="导线规格"></td>
                            <td><span class="pricing-sm-status-tag" data-field="组价计算状态"></span></td>
                            <td>
                                <button class="pricing-shared-btn pricing-shared-btn-primary" onclick="selectIndicators(this)" data-section-id="">组价计算</button>
                                <button class="pricing-shared-btn pricing-shared-btn-secondary" onclick="viewPricingResult(this)" data-section-id="" style="display:none;">组价结果</button>
                                <button class="pricing-shared-btn pricing-shared-btn-delete" onclick="deleteSection(this)" data-section-id="">删除</button>
                            </td>
                        </tr>
                    </template>
                </tbody>
            </table>
        </div>
        
        <!-- 添加分页包装器 -->
        <div class="pricing-sm-pagination-wrapper">
            <div class="pricing-sm-pagination">
                <button class="active">1</button>
                <button>2</button>
                <button>3</button>
                <button>4</button>
                <button>5</button>
            </div>
        </div>
    </div>
</section>

<!-- 新建特征段模态框 -->
<div class="pricing-shared-modal" id="createSectionModal">
    <div class="pricing-shared-modal-content">
        <span class="pricing-shared-modal-close" onclick="closeCreateSectionModal()">&times;</span>
        <h2>新建特征段</h2>
        <form id="createSectionForm" class="pricing-sm-add-section-form">
            <div class="pricing-sm-form-column">
                <div class="pricing-sm-form-group">
                    <label for="section-name" class="pricing-sm-form-label">特征段名称</label>
                    <input type="text" class="pricing-shared-input" id="section-name" required>
                </div>
                
                <div class="pricing-sm-form-group">
                    <label for="section-line-length" class="pricing-sm-form-label">线路长度(km)</label>
                    <input type="number" class="pricing-shared-input" id="section-line-length" required>
                </div>
                
                <div class="pricing-sm-form-group">
                    <label for="section-wind-speed" class="pricing-sm-form-label">风速(m/s)</label>
                    <input type="number" class="pricing-shared-input" id="section-wind-speed" required>
                </div>
                
                <div class="pricing-sm-form-group">
                    <label for="section-ice-thickness" class="pricing-sm-form-label">覆冰(mm)</label>
                    <input type="number" class="pricing-shared-input" id="section-ice-thickness" required>
                </div>
                
                <div class="pricing-sm-form-group">
                    <label for="section-circuit-count" class="pricing-sm-form-label">回路数</label>
                    <select class="pricing-shared-select" id="section-circuit-count" required>
                        <option value="">请选择</option>
                        <option value="单回路">单回路</option>
                        <option value="双回路">双回路</option>
                    </select>
                </div>
                
                <div class="pricing-sm-form-group">
                    <label for="section-wire-spec" class="pricing-sm-form-label">导线规格</label>
                    <input type="text" class="pricing-shared-input" id="section-wire-spec" required>
                </div>
                
                <div class="pricing-sm-form-group">
                    <label for="section-remark" class="pricing-sm-form-label">备注</label>
                    <textarea class="pricing-shared-textarea" id="section-remark" rows="3"></textarea>
                </div>
            </div>
            
            <div class="pricing-sm-form-actions">
                <button type="button" class="pricing-shared-btn pricing-shared-btn-primary" onclick="saveNewSection()">保存</button>
                <button type="button" class="pricing-shared-btn pricing-shared-btn-secondary" onclick="closeCreateSectionModal()">取消</button>
            </div>
        </form>
    </div>
</div>

<!-- 添加组价计算模态框 -->
<div class="pricing-shared-modal" id="indicatorSelectModal">
    <div class="pricing-shared-modal-content pricing-sm-large-modal">
        <span class="pricing-shared-modal-close" onclick="closeIndicatorSelectModal()">&times;</span>
        <iframe id="indicatorSelectFrame" style="width: 100%; height: calc(100% - 0px); border: none; overflow: visible;"></iframe>
    </div>
</div>

<script src="{{ url_for('static', filename='js/common.js') }}"></script>
<script src="{{ url_for('static', filename='js/section_management.js') }}"></script>

<!-- 注入工程ID -->
<script type="text/javascript">
// ========= 演示模式标记 =========
// true: 仅前端演示，不调用后端接口刷新列表。
// 切换为 false 则恢复实际接口刷新。
window.demoMode = true;

// 手动刷新工程信息函数
function manualRefreshProjectInfo() {
    console.log('手动刷新工程信息被触发');
    if (window.projectId) {
        console.log('当前工程ID:', window.projectId);
        
        // 检查window.updateProjectInfoPanel是否存在
        if (typeof window.updateProjectInfoPanel === 'function') {
            console.log('调用updateProjectInfoPanel函数');
            window.updateProjectInfoPanel().then(() => {
                console.log('工程信息更新完成');
                // 刷新后检查元素状态
                setTimeout(checkProjectInfoElements, 500);
            }).catch(error => {
                console.error('更新工程信息出错:', error);
            });
        } else {
            console.error('updateProjectInfoPanel函数不存在');
            
            // 尝试直接设置演示数据
            const demoProjectInfo = {
                工程名称: '广州-海南500kV输电线路工程',
                电压等级: '500kV',
                线路总长度: '72.3'
            };
            
            document.getElementById('projectName').textContent = demoProjectInfo.工程名称;
            document.getElementById('voltageLevel').textContent = demoProjectInfo.电压等级;
            document.getElementById('totalLineLength').textContent = demoProjectInfo.线路总长度;
            console.log('已手动设置演示数据');
            
            // 更新后检查元素状态
            setTimeout(checkProjectInfoElements, 500);
        }
    } else {
        console.error('工程ID不存在，无法刷新工程信息');
        alert('未找到工程ID，请返回工程列表重新选择工程');
    }
}

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    console.log('section_management.html 已加载，当前projectId:', window.projectId);
    
    // 检查工程信息面板元素状态
    function checkProjectInfoElements() {
        console.log('=========== 检查工程信息面板元素 ===========');
        const projectNameEl = document.getElementById('projectName');
        const voltageLevelEl = document.getElementById('voltageLevel');
        const totalLineLengthEl = document.getElementById('totalLineLength');
        
        console.log('projectName元素:', projectNameEl);
        console.log('projectName元素可见性:', projectNameEl ? getComputedStyle(projectNameEl).display : 'element not found');
        console.log('projectName元素内容:', projectNameEl ? projectNameEl.textContent : 'element not found');
        
        console.log('voltageLevel元素:', voltageLevelEl);
        console.log('voltageLevel元素可见性:', voltageLevelEl ? getComputedStyle(voltageLevelEl).display : 'element not found');
        console.log('voltageLevel元素内容:', voltageLevelEl ? voltageLevelEl.textContent : 'element not found');
        
        console.log('totalLineLength元素:', totalLineLengthEl);
        console.log('totalLineLength元素可见性:', totalLineLengthEl ? getComputedStyle(totalLineLengthEl).display : 'element not found');
        console.log('totalLineLength元素内容:', totalLineLengthEl ? totalLineLengthEl.textContent : 'element not found');
        
        console.log('工程信息面板:', document.querySelector('.pricing-sm-project-info'));
        console.log('工程信息面板可见性:', getComputedStyle(document.querySelector('.pricing-sm-project-info')).display);
        console.log('=========== 检查完成 ===========');
    }
    
    // 设置一个定时器延迟执行检查，确保页面已完全加载
    setTimeout(checkProjectInfoElements, 2000);
    
    // 确保window.loadSections函数存在
    if (typeof window.loadSections !== 'function') {
        console.error('window.loadSections函数不存在，等待section_management.js加载完成');
        
        // 等待section_management.js加载完成
        const checkLoadSections = setInterval(() => {
            if (typeof window.loadSections === 'function') {
                console.log('检测到window.loadSections函数已加载');
                clearInterval(checkLoadSections);
                
                // 如果已有工程ID，加载特征段数据
                if (window.projectId) {
                    console.log('检测到工程ID，加载特征段数据:', window.projectId);
                    window.loadSections();
                    // 在加载完成后再次检查工程信息面板元素
                    setTimeout(checkProjectInfoElements, 1000);
                } else {
                    // 如果没有工程ID，尝试从URL中获取
                    const urlParams = new URLSearchParams(window.location.search);
                    const projectId = urlParams.get('projectId');
                    if (projectId) {
                        console.log('从URL中获取工程ID:', projectId);
                        window.projectId = projectId;
                        window.loadSections();
                        // 在加载完成后再次检查工程信息面板元素
                        setTimeout(checkProjectInfoElements, 1000);
                    } else {
                        console.warn('未找到工程ID，无法加载特征段数据');
                    }
                }
            }
        }, 100);
    } else if (window.projectId) {
        // 如果已有工程ID且loadSections函数存在，直接加载特征段数据
        console.log('检测到工程ID和loadSections函数，加载特征段数据:', window.projectId);
        window.loadSections();
        // 在加载完成后再次检查工程信息面板元素
        setTimeout(checkProjectInfoElements, 1000);
    } else {
        // 如果没有工程ID，尝试从URL中获取
        const urlParams = new URLSearchParams(window.location.search);
        const projectId = urlParams.get('projectId');
        if (projectId) {
            console.log('从URL中获取工程ID:', projectId);
            window.projectId = projectId;
            window.loadSections();
            // 在加载完成后再次检查工程信息面板元素
            setTimeout(checkProjectInfoElements, 1000);
        } else {
            console.warn('未找到工程ID，无法加载特征段数据');
        }
    }
    
    // 监听window.projectId的变化
    let lastProjectId = window.projectId;
    setInterval(() => {
        if (window.projectId !== lastProjectId) {
            console.log('检测到window.projectId变化:', lastProjectId, '->', window.projectId);
            lastProjectId = window.projectId;
            
            // 如果loadSections函数存在，加载特征段数据
            if (typeof window.loadSections === 'function' && window.projectId) {
                console.log('projectId变化后调用loadSections');
                window.loadSections();
            }
        }
    }, 300);
});

// 返回工程管理页面
function backToProjects() {
    // 优先查找新版 class
    let wsContent = document.querySelector('.pricing-ws-content');
    if (!wsContent && window.parent) {
        try {
            wsContent = window.parent.document.querySelector('.pricing-ws-content');
        } catch (e) {}
    }
    if (wsContent) {
        wsContent.classList.remove('pricing-ws-panels-expanded');
        wsContent.classList.add('pricing-ws-panels-collapsed');
        return;
    }
}

// 显示新建特征段模态框
function showCreateSectionModal() {
    console.log('showCreateSectionModal被调用，当前projectId:', window.projectId);
    
    // 填充默认演示数据
    const defaultData = {
        sectionName: '广州段',
        lineLength: '30',
        windSpeed: '27',
        iceThickness: '0',
        circuitCount: '双回路',
        wireSpec: 'JL/LB20A-630/45',
        remark: '广州段示例数据'
    };

    // 填充表单数据
    document.getElementById('section-name').value = defaultData.sectionName;
    document.getElementById('section-line-length').value = defaultData.lineLength;
    document.getElementById('section-wind-speed').value = defaultData.windSpeed;
    document.getElementById('section-ice-thickness').value = defaultData.iceThickness;
    document.getElementById('section-circuit-count').value = defaultData.circuitCount;
    document.getElementById('section-wire-spec').value = defaultData.wireSpec;
    document.getElementById('section-remark').value = defaultData.remark;
    
    // 显示模态框
    document.getElementById('createSectionModal').classList.add('show');
}

// 关闭新建特征段模态框
function closeCreateSectionModal() {
    document.getElementById('createSectionModal').classList.remove('show');
    
    // 清空表单
    document.getElementById('createSectionForm').reset();
}

// 显示提示信息
function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `toast ${type}`;
    toast.textContent = message;
    document.body.appendChild(toast);

    // 3秒后自动移除
    setTimeout(() => {
        toast.remove();
    }, 3000);
}

// 显示组价计算模态框
function selectIndicators(button) {
    console.log('selectIndicators被调用，当前projectId:', window.projectId);
       
    const sectionId = button.getAttribute('data-section-id');
    console.log('特征段ID:', sectionId);
    
    const modal = document.getElementById('indicatorSelectModal');
    const frame = document.getElementById('indicatorSelectFrame');
    
    // 获取当前行的特征段数据
    const row = button.closest('tr');
    
    // 获取单元格内容
    const cells = row.cells;
    const sectionData = {
        工程名称: document.getElementById('projectName').textContent,
        特征段名称: cells[0].textContent,
        线路长度: cells[1].textContent,
        风速: cells[2].textContent,
        覆冰: cells[3].textContent,
        回路数: cells[4].textContent,
        导线规格: cells[5].textContent
    };
    
    console.log('特征段数据:', sectionData);
    
    // 将数据存储到 sessionStorage
    sessionStorage.setItem('selectedSectionData', JSON.stringify(sectionData));
    
    // 设置iframe的src
    // 使用新路由 section_pricingSum_content 替代 indicator_select_content
    const iframeSrc = `/quick_pricing/section_pricingSum_content/${window.projectId}/${sectionId}`;
    console.log('iframe src:', iframeSrc);
    frame.src = iframeSrc;
    modal.classList.add('show');
}

// 关闭组价计算模态框
function closeIndicatorSelectModal() {
    const modal = document.getElementById('indicatorSelectModal');
    modal.classList.remove('show');

    // 在演示模式下，不刷新列表，而是直接修改当前特征段的状态
    if (window.demoMode) {
        console.log('演示模式，手动更新特征段状态');
        
        // 查找当前正在操作的特征段行
        const tbody = document.getElementById('sectionTableBody');
        const rows = tbody.getElementsByTagName('tr');
        
        // 获取iframe中的特征段名称
        let sectionName = '';
        try {
            const iframe = document.getElementById('indicatorSelectFrame');
            if (iframe && iframe.contentWindow) {
                const sectionNameElement = iframe.contentDocument.getElementById('sectionName');
                if (sectionNameElement) {
                    sectionName = sectionNameElement.textContent;
                    console.log('从iframe获取到特征段名称:', sectionName);
                }
            }
        } catch(e) {
            console.error('从iframe获取特征段名称失败:', e);
        }
        
        // 如果没有获取到名称，就默认修改第一个"未组价"的特征段
        if (!sectionName) {
            console.log('未获取到特征段名称，将修改第一个未组价的特征段');
            for (let row of rows) {
                const statusCell = row.cells[6];
                const statusTag = statusCell.querySelector('.pricing-sm-status-tag');
                if (statusTag && statusTag.textContent === '未组价') {
                    updateRowButtons(row);
                    break;
                }
            }
        } else {
            // 根据特征段名称查找对应行
            for (let row of rows) {
                const nameCell = row.cells[0];
                if (nameCell.textContent.trim() === sectionName.trim()) {
                    updateRowButtons(row);
                    break;
                }
            }
        }
        
        return;
    }

    // 非演示模式才调用后端刷新
    if (typeof window.loadSections === 'function') {
        console.log('调用window.loadSections刷新特征段列表');
        window.loadSections();
    } else {
        console.error('window.loadSections函数不存在');
    }
}

// 更新行按钮状态
function updateRowButtons(row) {
    // 更新状态标签
    const statusCell = row.cells[6];
    const statusTag = statusCell.querySelector('.pricing-sm-status-tag');
    statusTag.textContent = '已组价';
    statusTag.className = 'pricing-sm-status-tag pricing-sm-status-success';
    
    // 更新按钮
    const operationCell = row.cells[7];
    const pricingBtn = operationCell.querySelector('[onclick*="selectIndicators"]');
    const resultBtn = operationCell.querySelector('[onclick*="viewPricingResult"]');
    
    if (pricingBtn) {
        pricingBtn.textContent = '重新组价';
    }
    
    if (resultBtn) {
        resultBtn.style.display = 'inline-block';
    }
    
    console.log('已更新行按钮状态');
}

// 删除特征段
function deleteSection(button) {
    console.log('deleteSection被调用，当前projectId:', window.projectId);
      
    const sectionId = button.dataset.sectionId;
    console.log('要删除的特征段ID:', sectionId);
    
    if (!confirm('确定要删除该特征段吗？')) {
        return;
    }
    
    const url = `/api/pricing/projects/${window.projectId}/feature_sections/${sectionId}`;
    console.log('删除特征段请求URL:', url);
    
    fetch(url, {
        method: 'DELETE'
    })
    .then(response => {
        console.log('删除特征段响应状态:', response.status, response.statusText);
        if (!response.ok) {
            throw new Error('删除特征段失败');
        }
        showToast('特征段删除成功', 'success');
        if (typeof window.loadSections === 'function') {
            window.loadSections();
        } else {
            console.error('window.loadSections函数不存在');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('删除特征段失败: ' + error.message, 'error');
        });
}

// 更新特征段状态
async function updateSectionStatus(sectionData) {
    console.log('updateSectionStatus被调用，当前projectId:', window.projectId);   
    
    try {
        // 查找对应的特征段行
        const tbody = document.getElementById('sectionTableBody');
        const rows = tbody.getElementsByTagName('tr');
        
        for (let row of rows) {
            const nameCell = row.cells[0];
            if (nameCell.textContent.trim() === (sectionData.特征段名称 || '').trim()) {
                console.log('找到要更新的特征段行:', nameCell.textContent);
                
                // 演示模式：直接更新UI
                if (window.demoMode) {
                    updateRowButtons(row);
                    return;
                }
                
                // 非演示模式：调用API
                const sectionId = row.querySelector('[data-section-id]').getAttribute('data-section-id');
                console.log('要更新的特征段ID:', sectionId);
                
                const url = `/api/pricing/projects/${window.projectId}/feature_sections/${sectionId}`;
                console.log('更新特征段状态请求URL:', url);
                
                const response = await fetch(url, {
                    method: 'PATCH',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        ...sectionData,
                        序号: parseInt(sectionId),
                        工程序号: parseInt(window.projectId)
                    })
                });
                
                console.log('更新特征段状态响应状态:', response.status, response.statusText);

                if (!response.ok) {
                    throw new Error('更新失败');
                }
                
                showToast('特征段状态更新成功', 'success');
                break;
            }
        }
    } catch (error) {
        console.error('更新特征段状态失败:', error);
        showToast('更新特征段状态失败', 'error');
    }
}

// 查看组价结果
function viewPricingResult(button) {
    console.log('viewPricingResult被调用，当前projectId:', window.projectId);
    
    const sectionId = button.getAttribute('data-section-id');
    console.log('特征段ID:', sectionId);
    
    // 获取当前行的特征段数据
    const row = button.closest('tr');
    
    // 获取单元格内容
    const cells = row.cells;
    const sectionData = {
        工程名称: document.getElementById('projectName').textContent,
        特征段名称: cells[0].textContent,
        线路长度: cells[1].textContent,
        风速: cells[2].textContent,
        覆冰: cells[3].textContent,
        回路数: cells[4].textContent,
        导线规格: cells[5].textContent,
        组价计算状态: cells[6].querySelector('.pricing-sm-status-tag').textContent
    };
    
    console.log('特征段数据:', sectionData);
    
    // 创建并显示组价结果模态框
    showPricingResultModal(sectionData);
}

// 显示组价结果模态框
function showPricingResultModal(sectionData) {
    // 创建模态框HTML
    const modalHtml = `
    <div class="pricing-shared-modal show" id="pricingResultModal">
        <div class="pricing-shared-modal-content pricing-sm-large-modal" style="padding: 10px;">
            <span class="pricing-shared-modal-close" onclick="closePricingResultModal()">&times;</span>
            <h2>${sectionData.特征段名称} - 组价结果</h2>
            <div class="feature-info">
                <div class="feature-info-item name">
                    <label>特征段名称</label>
                    <span>${sectionData.特征段名称}</span>
                </div>
                <div class="feature-info-item length">
                    <label>线路长度</label>
                    <span>${sectionData.线路长度}</span>
                </div>
                <div class="feature-info-item wind-speed">
                    <label>风速</label>
                    <span>${sectionData.风速}</span>
                </div>
                <div class="feature-info-item ice-thickness">
                    <label>覆冰</label>
                    <span>${sectionData.覆冰}</span>
                </div>
                <div class="feature-info-item circuit-count">
                    <label>回路数</label>
                    <span>${sectionData.回路数}</span>
                </div>
                <div class="feature-info-item wire-spec">
                    <label>导线规格</label>
                    <span>${sectionData.导线规格}</span>
                </div>
            </div>
            
            <div class="pricing-result-container">
                <h3>组价计算结果</h3>
                <div class="tab-header">
                    <div class="indicator-tabs">
                        <button class="tab-button active" onclick="switchResultTab('benti')">本体费用指标</button>
                        <button class="tab-button" onclick="switchResultTab('qita')">其他费用指标</button>
                    </div>
                </div>
                
                <div id="bentiResultPanel" class="result-panel">
                    <div class="result-table-container">
                        <table class="result-table">
                            <thead>
                                <tr>
                                    <th>指标名称</th>
                                    <th>单公里量</th>
                                    <th>指标总量</th>
                                    <th>指标单价(元)</th>
                                    <th>单公里组价(元)</th>
                                </tr>
                            </thead>
                            <tbody id="bentiResultBody">
                                <!-- 演示数据 - 本体费用指标 -->
                                <tr>
                                    <td>铁塔基数(基)</td>
                                    <td>2.65</td>
                                    <td>79.50</td>
                                    <td>10000</td>
                                    <td>26500</td>
                                </tr>
                                <tr>
                                    <td>直线塔(基)</td>
                                    <td>2.12</td>
                                    <td>63.60</td>
                                    <td>8000</td>
                                    <td>16960</td>
                                </tr>
                                <tr>
                                    <td>耐张塔(基)</td>
                                    <td>0.53</td>
                                    <td>15.90</td>
                                    <td>12000</td>
                                    <td>6360</td>
                                </tr>
                                <tr>
                                    <td>导线(t)</td>
                                    <td>2.98</td>
                                    <td>89.40</td>
                                    <td>15000</td>
                                    <td>44700</td>
                                </tr>
                                <tr>
                                    <td>塔材(t)</td>
                                    <td>15.89</td>
                                    <td>476.70</td>
                                    <td>7000</td>
                                    <td>111230</td>
                                </tr>
                                <tr>
                                    <td>地线(t)</td>
                                    <td>0.85</td>
                                    <td>25.50</td>
                                    <td>12000</td>
                                    <td>10200</td>
                                </tr>
                                <tr>
                                    <td>OPGW光缆(km)</td>
                                    <td>1.00</td>
                                    <td>30.00</td>
                                    <td>35000</td>
                                    <td>35000</td>
                                </tr>
                                <tr>
                                    <td>绝缘子串(组)</td>
                                    <td>15.90</td>
                                    <td>477.00</td>
                                    <td>1500</td>
                                    <td>23850</td>
                                </tr>
                                <tr>
                                    <td>金具(套)</td>
                                    <td>15.90</td>
                                    <td>477.00</td>
                                    <td>800</td>
                                    <td>12720</td>
                                </tr>
                                <tr>
                                    <td>基础混凝土(m³)</td>
                                    <td>53.00</td>
                                    <td>1590.00</td>
                                    <td>600</td>
                                    <td>31800</td>
                                </tr>
                                <tr>
                                    <td>基础钢筋(t)</td>
                                    <td>3.18</td>
                                    <td>95.40</td>
                                    <td>5500</td>
                                    <td>17490</td>
                                </tr>
                                <tr>
                                    <td>接地装置(套)</td>
                                    <td>2.65</td>
                                    <td>79.50</td>
                                    <td>1200</td>
                                    <td>3180</td>
                                </tr>
                                <tr>
                                    <td>防振锤(个)</td>
                                    <td>10.60</td>
                                    <td>318.00</td>
                                    <td>200</td>
                                    <td>2120</td>
                                </tr>
                                <tr>
                                    <td>间隔棒(个)</td>
                                    <td>31.80</td>
                                    <td>954.00</td>
                                    <td>150</td>
                                    <td>4770</td>
                                </tr>
                                <tr>
                                    <td>警示牌(块)</td>
                                    <td>2.65</td>
                                    <td>79.50</td>
                                    <td>100</td>
                                    <td>265</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <div id="qitaResultPanel" class="result-panel" style="display:none;">
                    <div class="result-table-container">
                        <table class="result-table">
                            <thead>
                                <tr>
                                    <th>指标名称</th>
                                    <th>单公里量</th>
                                    <th>指标总量</th>
                                    <th>指标单价(元)</th>
                                    <th>单公里组价(元)</th>
                                </tr>
                            </thead>
                            <tbody id="qitaResultBody">
                                <!-- 演示数据 - 其他费用指标 -->
                                <tr>
                                    <td>项目建设管理费(万元)</td>
                                    <td>7.95</td>
                                    <td>238.50</td>
                                    <td>10000</td>
                                    <td>79500</td>
                                </tr>
                                <tr>
                                    <td>项目法人管理费(万元)</td>
                                    <td>3.97</td>
                                    <td>119.10</td>
                                    <td>10000</td>
                                    <td>39700</td>
                                </tr>
                                <tr>
                                    <td>招标费(万元)</td>
                                    <td>1.59</td>
                                    <td>47.70</td>
                                    <td>10000</td>
                                    <td>15900</td>
                                </tr>
                                <tr>
                                    <td>工程监理费(万元)</td>
                                    <td>3.18</td>
                                    <td>95.40</td>
                                    <td>10000</td>
                                    <td>31800</td>
                                </tr>
                                <tr>
                                    <td>工程保险费(万元)</td>
                                    <td>2.38</td>
                                    <td>71.40</td>
                                    <td>10000</td>
                                    <td>23800</td>
                                </tr>
                                <tr>
                                    <td>设计费(万元)</td>
                                    <td>3.50</td>
                                    <td>105.00</td>
                                    <td>10000</td>
                                    <td>35000</td>
                                </tr>
                                <tr>
                                    <td>环境影响评价费(万元)</td>
                                    <td>0.80</td>
                                    <td>24.00</td>
                                    <td>10000</td>
                                    <td>8000</td>
                                </tr>
                                <tr>
                                    <td>水土保持评价费(万元)</td>
                                    <td>0.70</td>
                                    <td>21.00</td>
                                    <td>10000</td>
                                    <td>7000</td>
                                </tr>
                                <tr>
                                    <td>地质灾害评估费(万元)</td>
                                    <td>0.60</td>
                                    <td>18.00</td>
                                    <td>10000</td>
                                    <td>6000</td>
                                </tr>
                                <tr>
                                    <td>施工图审查费(万元)</td>
                                    <td>0.40</td>
                                    <td>12.00</td>
                                    <td>10000</td>
                                    <td>4000</td>
                                </tr>
                                <tr>
                                    <td>工程质量监督费(万元)</td>
                                    <td>0.90</td>
                                    <td>27.00</td>
                                    <td>10000</td>
                                    <td>9000</td>
                                </tr>
                                <tr>
                                    <td>安全评价费(万元)</td>
                                    <td>0.50</td>
                                    <td>15.00</td>
                                    <td>10000</td>
                                    <td>5000</td>
                                </tr>
                                <tr>
                                    <td>征地费(万元)</td>
                                    <td>12.50</td>
                                    <td>375.00</td>
                                    <td>10000</td>
                                    <td>125000</td>
                                </tr>
                                <tr>
                                    <td>青苗补偿费(万元)</td>
                                    <td>3.20</td>
                                    <td>96.00</td>
                                    <td>10000</td>
                                    <td>32000</td>
                                </tr>
                                <tr>
                                    <td>临时用地费(万元)</td>
                                    <td>2.80</td>
                                    <td>84.00</td>
                                    <td>10000</td>
                                    <td>28000</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            
            <div class="modal-buttons">
                <button class="btn btn-secondary" onclick="closePricingResultModal()">关闭</button>
                <button class="btn btn-primary" onclick="exportPricingResult()">导出结果</button>
            </div>
        </div>
    </div>
    `;
    
    // 添加模态框到页面
    document.body.insertAdjacentHTML('beforeend', modalHtml);
    
    // 添加样式
    const styleElement = document.createElement('style');
    styleElement.textContent = `
        .pricing-result-container {
            margin-top: 20px;
        }
        
        .result-table-container {
            max-height: 400px;
            overflow-y: auto;
            margin-top: 15px;
            border: 1px solid #eee;
        }
        
        .result-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .result-table th, .result-table td {
            padding: 10px;
            border: 1px solid #ddd;
            text-align: left;
        }
        
        .result-table th {
            background-color: #f5f5f5;
            font-weight: 500;
            position: sticky;
            top: 0;
            z-index: 10;
            box-shadow: 0 1px 1px rgba(0,0,0,0.1);
        }
        
        .result-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        .feature-info {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 5px;
            margin-top: 15px;
        }
        
        .feature-info-item {
            display: flex;
            flex-direction: column;
            min-width: 120px;
        }
        
        .feature-info-item label {
            font-size: 12px;
            color: #666;
            margin-bottom: 5px;
        }
        
        .feature-info-item span {
            font-weight: bold;
            color: #333;
        }
        
        .indicator-tabs {
            display: flex;
            border-bottom: 1px solid #ddd;
            margin-bottom: 15px;
        }
        
        .tab-button {
            padding: 8px 16px;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 14px;
            border-bottom: 2px solid transparent;
        }
        
        .tab-button.active {
            border-bottom: 2px solid var(--primary-color, #1890ff);
            color: var(--primary-color, #1890ff);
            font-weight: 500;
        }
        
        .modal-buttons {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            margin-top: 20px;
            padding-top: 15px;
            border-top: 1px solid #eee;
        }
        
        #pricingResultModal .pricing-shared-modal-content {
            padding: 20px;
            max-height: 90vh;
            overflow-y: auto;
        }
    `;
    document.head.appendChild(styleElement);
}

// 关闭组价结果模态框
function closePricingResultModal() {
    const modal = document.getElementById('pricingResultModal');
    if (modal) {
        modal.remove();
    }
}

// 切换组价结果标签页
function switchResultTab(type) {
    const bentiPanel = document.getElementById('bentiResultPanel');
    const qitaPanel = document.getElementById('qitaResultPanel');
    const buttons = document.querySelectorAll('#pricingResultModal .tab-button');
    
    if (type === 'benti') {
        bentiPanel.style.display = 'block';
        qitaPanel.style.display = 'none';
        buttons[0].classList.add('active');
        buttons[1].classList.remove('active');
    } else {
        bentiPanel.style.display = 'none';
        qitaPanel.style.display = 'block';
        buttons[0].classList.remove('active');
        buttons[1].classList.add('active');
    }
}

// 导出组价结果
function exportPricingResult() {
    showToast('组价结果导出成功', 'success');
}
</script> 

<!-- 移除所有与工程信息面板相关的JS强制样式和多余的演示数据脚本 -->